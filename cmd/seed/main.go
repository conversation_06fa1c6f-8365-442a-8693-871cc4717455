package main

import (
	"log"
	"ops-api/pkg/database"

	"ops-api/config"
	"ops-api/internal/adapters/repository"
	"ops-api/internal/core/domain"
	"ops-api/internal/core/services"
)

func main() {
	cfg := config.Load()

	// Initialize database
	db, err := database.NewDatabase(cfg)
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}
	defer db.Close()

	// Initialize repositories and services
	userTypeRepo := repository.NewUserTypeRepository(db.GetDB())
	userTypeService := services.NewUserTypeService(userTypeRepo)

	jobStatusRepo := repository.NewJobStatusRepository(db.GetDB())
	jobStatusService := services.NewJobStatusService(jobStatusRepo)

	serverStatusRepo := repository.NewServerStatusRepository(db.GetDB())
	serverStatusService := services.NewServerStatusService(serverStatusRepo)

	// Seed default user types
	defaultUserTypes := []struct {
		name        string
		description string
		isActive    bool
		isAdmin     bool
		isMember    bool
		isSale      bool
	}{
		{domain.UserTypeAdmin, "Administrator with full system access", true, true, false, false},
		{domain.UserTypeMember, "Member with standard access", true, false, true, false},
		{domain.UserTypeSale, "Sale user with sale access", true, false, false, true},
	}

	for _, ut := range defaultUserTypes {
		// Check if user type already exists using GetByName
		_, err := userTypeService.GetByName(ut.name)
		exists := err == nil

		if !exists {
			_, err := userTypeService.Create(ut.name, ut.description, ut.isActive, ut.isAdmin, ut.isMember, ut.isSale)
			if err != nil {
				log.Printf("Error creating user type %s: %v", ut.name, err)
			} else {
				log.Printf("Created user type: %s", ut.name)
			}
		} else {
			log.Printf("User type %s already exists, skipping", ut.name)
		}
	}

	// Seed default job statuses
	defaultJobStatuses := []string{
		"Creating",
		"Pending",
		"Processing",
		"Failed",
		"Successed",
	}

	for _, status := range defaultJobStatuses {
		// Check if job status already exists
		_, err := jobStatusService.GetByName(status)
		exists := err == nil

		if !exists {
			_, err := jobStatusService.Create(status)
			if err != nil {
				log.Printf("Error creating job status %s: %v", status, err)
			} else {
				log.Printf("Created job status: %s", status)
			}
		} else {
			log.Printf("Job status %s already exists, skipping", status)
		}
	}

	// Seed default server statuses
	defaultServerStatuses := []string{
		string(domain.ServerStatusUnpublished),
		string(domain.ServerStatusCreating),
		string(domain.ServerStatusActive),
		string(domain.ServerStatusStopped),
		string(domain.ServerStatusUpdating),
		string(domain.ServerStatusMaintenance),
		string(domain.ServerStatusDeleting),
		string(domain.ServerStatusError),
		string(domain.ServerStatusDestroyed),
	}

	for _, status := range defaultServerStatuses {
		// Check if server status already exists
		_, err := serverStatusService.GetByName(status)
		exists := err == nil

		if !exists {
			_, err := serverStatusService.Create(status)
			if err != nil {
				log.Printf("Error creating server status %s: %v", status, err)
			} else {
				log.Printf("Created server status: %s", status)
			}
		} else {
			log.Printf("Server status %s already exists, skipping", status)
		}
	}

	log.Println("Seed completed successfully!")
}
