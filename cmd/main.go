package main

import (
	"context"
	"log"
	"ops-api/pkg/database"
	"os"
	"os/signal"
	"syscall"
	"time"

	"ops-api/config"
	"ops-api/internal/adapters/http"
)

func main() {
	cfg := config.Load()

	// Initialize database
	db, err := database.NewDatabase(cfg)
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}
	defer db.Close()

	// Initialize HTTP server
	server := http.NewServer(
		db.GetDB(),
		cfg,
	)

	// Graceful shutdown
	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)

	// Use the server's pool service to start the server instead of direct goroutine
	serverTask := func() {
		if err := server.Start(); err != nil {
			log.Printf("Server error: %v", err)
		}
	}

	poolService := server.GetPoolService()
	if err := poolService.Submit(serverTask); err != nil {
		log.Printf("Failed to submit server start task to pool service: %v", err)
		// Fallback to direct goroutine if pool service fails
		go func() {
			defer func() {
				if r := recover(); r != nil {
					log.Printf("Recovered from panic in server start fallback: %v", r)
				}
			}()
			serverTask()
		}()
	}

	<-c
	log.Println("Shutting down server...")

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	if err := server.Shutdown(ctx); err != nil {
		log.Printf("Server forced to shutdown: %v", err)
	}

	log.Println("Server exiting")
}
