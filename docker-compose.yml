version: '3.8'

services:
  app:
    container_name: blacking-ops-admin
    build:
      context: ../ops-admin-app
      dockerfile: Dockerfile
    restart: always
    ports:
      - "3000:3000"
    environment:
      - API_ENDPOINT=http://api:8080/api/v1
      - MASTER_DOMAIN=kabao.dev
    networks:
      - ops-admin-network
    depends_on:
      - api
  api:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: ops-admin-api
    ports:
      - "8080:8080"
    environment:
      - ENV=production
      - DB_DRIVER=postgres
      - DB_HOST=yamanote.proxy.rlwy.net
      - DB_PORT=41166
      - DB_USER=postgres
      - DB_PASSWORD=dzoSCdymnNFWGAsoGlwxqSMumDBbMNTu
      - DB_NAME=railway
      - AUTO_MIGRATE=false
      - PORT=8080
      - JWT_SECRET=secret
      - DO_TOKEN=***********************************************************************
      - TF_BACKEND_DB=postgres://postgres:<EMAIL>:41166/terraform-state?sslmode=require
      - TF_BACKEND_SCHEMA_CLUSTER=cluster
      - TF_BACKEND_SCHEMA_RESOURCE=resource
      - TF_BACKEND_SCHEMA_DNS=dns
      - TF_OPERATION_ENDPOINT=http://ops-admin-api:8080/api/v1
      - CLOUDFLARE_API_TOKEN=****************************************
      - CLOUDFLARE_ACCOUNT_ID=af4d86bbc2a57d8e3cd99a749e3adda8
      - CLOUDFLARE_MASTER_ZONE_ID=ba24c276482312f9986da7b3b052fdc5
      - CLOUDFLARE_MASTER_ZONE_NAME=bugkeeper.space
    networks:
      - ops-admin-network
    volumes:
      - ./terraform:/app/terraform
      - ./tmp:/app/tmp
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  ops-admin-network:
    driver: bridge

