# Binaries
bin/
tmp/
*.log

# Git
.git
.gitignore

# Documentation
README.md
*.md

# Development files
#.env
.env.local
.env.development
.env.test
.env.production

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Go specific
vendor/
*.exe
*.exe~
*.dll
*.so
*.dylib
*.test
*.out
go.work
go.work.sum

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Terraform
#*.tfstate
#*.tfstate.*
#.terraform/
#.terraform.lock.hcl
#*.tfvars
#*.tfplan
