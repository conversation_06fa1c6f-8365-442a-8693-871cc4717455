.PHONY: build run dev clean test

# Build the application
build:
	go build -o bin/main cmd/main.go

# Run the application
run:
	go run cmd/main.go

# Run with hot reload using Air
dev:
	air

# Seed the database with initial data
seed:
	go run cmd/seed/main.go

# Clean build artifacts
clean:
	rm -rf bin/ tmp/

# Run tests
test:
	go test ./...

# Install dependencies
deps:
	go mod download
	go mod tidy

# Install Air (run once)
install-air:
	go install github.com/cosmtrek/air@latest
