package response

import "github.com/gofiber/fiber/v2"

// StandardResponse represents the standard API response format
type StandardResponse struct {
	Status  bool        `json:"status"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// Success creates a successful response
func Success(c *fiber.Ctx, statusCode int, message string, data interface{}) error {
	response := StandardResponse{
		Status:  true,
		Message: message,
		Data:    data,
	}
	return c.Status(statusCode).JSON(response)
}

// Error creates an error response
func Error(c *fiber.Ctx, statusCode int, message string) error {
	response := StandardResponse{
		Status:  false,
		Message: message,
		Data:    nil,
	}
	return c.Status(statusCode).JSO<PERSON>(response)
}

// SuccessWithoutData creates a successful response without data
func SuccessWithoutData(c *fiber.Ctx, statusCode int, message string) error {
	response := StandardResponse{
		Status:  true,
		Message: message,
	}
	return c.Status(statusCode).JSON(response)
}
