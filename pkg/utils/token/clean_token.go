package utils

import (
	"regexp"
	"strings"
)

func CleanToken(token string) string {
	// First, remove common header contaminations before regex cleaning
	cleaned := token

	// Remove common header patterns that might be mixed in
	contaminations := []string{
		"tion/json, text/plain, */*",
		"application/json, text/plain, */*",
		"application/json",
		"text/plain",
		"Accept:",
		"Accept",
		"Authorization:",
		"Authorization",
		"Bearer ",
		"Bearer",
		"Content-Type:",
		"Content-Type",
		", */*",
		"*/*",
		"/json",
		"tion",
		", text",
		"plain",
	}

	for _, contamination := range contaminations {
		cleaned = strings.ReplaceAll(cleaned, contamination, "")
	}

	// Remove any remaining non-JWT characters
	// JWT tokens contain only alphanumeric characters, dots, hyphens, and underscores
	re := regexp.MustCompile(`[^a-zA-Z0-9._-]`)
	cleaned = re.ReplaceAllString(cleaned, "")

	return strings.TrimSpace(cleaned)
}
