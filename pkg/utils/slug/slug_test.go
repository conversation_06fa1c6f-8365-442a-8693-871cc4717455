package slug

import (
	"testing"
)

func TestGenerate(t *testing.T) {
	tests := []struct {
		name     string
		input    string
		expected string
	}{
		{
			name:     "Simple name with spaces",
			input:    "My New Project",
			expected: "my-new-project",
		},
		{
			name:     "Name with hyphens and underscores",
			input:    "Test-Project_123",
			expected: "test-project-123",
		},
		{
			name:     "Name with special characters",
			input:    "Special!@#$%Characters",
			expected: "specialcharacters",
		},
		{
			name:     "Name with multiple spaces",
			input:    "  Multiple   Spaces  ",
			expected: "multiple-spaces",
		},
		{
			name:     "Already lowercase with hyphens",
			input:    "lowercase-already",
			expected: "lowercase-already",
		},
		{
			name:     "Empty string",
			input:    "",
			expected: "",
		},
		{
			name:     "Only special characters",
			input:    "!@#$%^&*()",
			expected: "",
		},
		{
			name:     "Mixed case with numbers",
			input:    "MyProject123",
			expected: "myproject123",
		},
		{
			name:     "Name with dots and commas",
			input:    "Project.Name,Version",
			expected: "projectnameversion",
		},
		{
			name:     "Name with consecutive hyphens",
			input:    "Project---Name",
			expected: "project-name",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := Generate(tt.input)
			if result != tt.expected {
				t.Errorf("Generate(%q) = %q, want %q", tt.input, result, tt.expected)
			}
		})
	}
}

func TestGenerateConsistency(t *testing.T) {
	input := "Test Project Name"
	expected := "test-project-name"

	// Run the function multiple times to ensure consistency
	for i := 0; i < 10; i++ {
		result := Generate(input)
		if result != expected {
			t.Errorf("Generate(%q) iteration %d = %q, want %q", input, i, result, expected)
		}
	}
}

func BenchmarkGenerate(b *testing.B) {
	input := "My Complex Project Name With Many Words"

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		Generate(input)
	}
}
