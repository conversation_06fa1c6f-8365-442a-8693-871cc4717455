package slug

import (
	"regexp"
	"strings"
)

// Generate creates a URL-friendly slug from the given text
// It converts to lowercase, replaces spaces and special characters with hyphens,
// and removes consecutive hyphens
func Generate(text string) string {
	if text == "" {
		return ""
	}

	// Convert to lowercase
	slug := strings.ToLower(text)

	// Replace spaces and underscores with hyphens
	slug = strings.ReplaceAll(slug, " ", "-")
	slug = strings.ReplaceAll(slug, "_", "-")

	// Remove all characters that are not alphanumeric or hyphens
	reg := regexp.MustCompile(`[^a-z0-9\-]`)
	slug = reg.ReplaceAllString(slug, "")

	// Replace multiple consecutive hyphens with a single hyphen
	reg = regexp.MustCompile(`-+`)
	slug = reg.ReplaceAllString(slug, "-")

	// Remove leading and trailing hyphens
	slug = strings.Trim(slug, "-")

	return slug
}
