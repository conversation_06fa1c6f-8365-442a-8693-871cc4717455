package errs

import "net/http"

type AppError struct {
	Code    int
	Message string
}

func (e AppError) Error() string {
	return e.Message
}

type ErrorResponse struct {
	Error   string `json:"error"`
	Message string `json:"message"`
	Code    int    `json:"code"`
}

func NewNotFoundError(message string) *ErrorResponse {
	return &ErrorResponse{
		Error:   "Not Found",
		Message: message,
		Code:    http.StatusNotFound,
	}
}

func NewBadRequestError(message string) *ErrorResponse {
	return &ErrorResponse{
		Error:   "Bad Request",
		Message: message,
		Code:    http.StatusBadRequest,
	}
}

func NewUnauthorizedError(message string) *ErrorResponse {
	return &ErrorResponse{
		Error:   "Unauthorized",
		Message: message,
		Code:    http.StatusUnauthorized,
	}
}

func NewForbiddenError(message string) *ErrorResponse {
	return &ErrorResponse{
		Error:   "Forbidden",
		Message: message,
		Code:    http.StatusForbidden,
	}
}

func NewConflictError(message string) *ErrorResponse {
	return &ErrorResponse{
		Error:   "Conflict",
		Message: message,
		Code:    http.StatusConflict,
	}
}

func NewInternalServerError(message string) *ErrorResponse {
	return &ErrorResponse{
		Error:   "Internal Server Error",
		Message: message,
		Code:    http.StatusInternalServerError,
	}
}

func NewNotUnexpectedError() error {
	return AppError{
		Code:    http.StatusInternalServerError,
		Message: "unexpected error",
	}
}
