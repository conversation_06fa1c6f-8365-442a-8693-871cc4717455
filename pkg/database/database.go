package database

import (
	"fmt"
	"ops-api/config"
	"ops-api/internal/core/domain"
	"ops-api/pkg/database/postgres"
	"ops-api/pkg/database/sqlite"
	"ops-api/pkg/utils/logs"

	"gorm.io/gorm"
)

// Database holds the database connection
type Database struct {
	DB *gorm.DB
}

// NewDatabase creates a new database connection based on configuration
func NewDatabase(cfg *config.Config) (*Database, error) {
	var db *gorm.DB
	var err error

	switch cfg.DBDriver {
	case "postgres":
		db, err = postgres.Connect(cfg)
	case "sqlite":
		db, err = sqlite.Connect(cfg)
	default:
		logs.Error(cfg.DBDriver)
		return nil, fmt.Errorf("unsupported database driver: %s", cfg.DBDriver)
	}

	if err != nil {
		logs.Error(err)
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	// Auto-migrate the schema only if enabled in config
	if cfg.AutoMigrate {
		err = db.AutoMigrate(
			&domain.UserType{},
			&domain.User{},
			&domain.Workspace{},
			&domain.Cluster{},
			&domain.Namespace{},
			&domain.Domain{},
			&domain.Deployment{},
			&domain.Environment{},
			&domain.Service{},
			&domain.Ingress{},
			&domain.IngressSpec{},
			&domain.JobStatus{},
			&domain.Job{},
			&domain.JobLog{},
			&domain.ServerStatus{},
			&domain.Order{},
			&domain.OrderDomain{},
			&domain.OrderNamespace{},
		)
		if err != nil {
			return nil, fmt.Errorf("failed to migrate database: %w", err)
		}
		logs.Info("Database auto-migration completed")
	} else {
		logs.Info("Database auto-migration disabled")
	}

	return &Database{DB: db}, nil
}

// GetDB returns the database connection
func (d *Database) GetDB() *gorm.DB {
	return d.DB
}

// Close closes the database connection
func (d *Database) Close() error {
	sqlDB, err := d.DB.DB()
	if err != nil {
		return err
	}
	return sqlDB.Close()
}
