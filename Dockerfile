# Multi-stage build for Go application with Terra<PERSON> and Helm
FROM golang:1.23-alpine AS builder

# Install build dependencies
RUN apk add --no-cache git ca-certificates tzdata

# Set working directory
WORKDIR /app

# Copy go mod files first for better layer caching
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy all source code including terraform directory
COPY . .

# Verify terraform files are present in builder stage
RUN echo "=== Builder stage verification ===" && \
    ls -la /app/ && \
    echo "=== Terraform directory in builder ===" && \
    ls -la /app/terraform/ && \
    echo "=== Resource directory in builder ===" && \
    ls -la /app/terraform/resource/ && \
    echo "=== Finding .tf files in builder ===" && \
    find /app/terraform -name "*.tf" -type f

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o main ./cmd/main.go

# Final stage
FROM alpine:latest

# Install runtime dependencies
RUN apk add --no-cache \
    ca-certificates \
    curl \
    wget \
    unzip \
    bash \
    git \
    openssh-client \
    && rm -rf /var/cache/apk/*

# Create app user
RUN addgroup -g 1001 appgroup && \
    adduser -u 1001 -G appgroup -s /bin/sh -D appuser

# Install Terraform
ENV TERRAFORM_VERSION=1.9.3
RUN wget https://releases.hashicorp.com/terraform/${TERRAFORM_VERSION}/terraform_${TERRAFORM_VERSION}_linux_amd64.zip && \
    unzip terraform_${TERRAFORM_VERSION}_linux_amd64.zip && \
    mv terraform /usr/local/bin/ && \
    rm terraform_${TERRAFORM_VERSION}_linux_amd64.zip && \
    chmod +x /usr/local/bin/terraform

# Install Helm
ENV HELM_VERSION=3.15.3
RUN wget https://get.helm.sh/helm-v${HELM_VERSION}-linux-amd64.tar.gz && \
    tar -zxvf helm-v${HELM_VERSION}-linux-amd64.tar.gz && \
    mv linux-amd64/helm /usr/local/bin/helm && \
    rm -rf helm-v${HELM_VERSION}-linux-amd64.tar.gz linux-amd64 && \
    chmod +x /usr/local/bin/helm

# Install kubectl (useful for Kubernetes operations)
ENV KUBECTL_VERSION=1.30.0
RUN wget https://dl.k8s.io/release/v${KUBECTL_VERSION}/bin/linux/amd64/kubectl && \
    mv kubectl /usr/local/bin/ && \
    chmod +x /usr/local/bin/kubectl

# Set working directory
WORKDIR /app

# Copy the binary from builder stage
COPY --from=builder /app/main .

# Copy terraform files with all subdirectories and contents
COPY --from=builder /app/terraform/ ./terraform/

# Create necessary directories with proper permissions
RUN mkdir -p /app/tmp /app/logs

# Change ownership to app user (including terraform files)
RUN chown -R appuser:appgroup /app

# Switch to non-root user
USER appuser

# Verify installations and directory structure
RUN terraform version && helm version && kubectl version --client && \
    ls -la /app/terraform/ && \
    find /app/terraform -type f -name "*.tf" | head -10

# Expose port (adjust based on your application's port)
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Run the application
CMD ["./main"]
