variable "provider_token" {
  type = string
  default = ""
  sensitive = true
}

variable "cluster_configs" {
  type = object({
    cluster_name = string
    region = string
    node_pool = object({
      pool_name = string
      size = string
      node_count = number
    })
  })
  default = {
    cluster_name =""
    region = ""
    node_pool = {
      pool_name = ""
      size = ""
      node_count = 0
    }
  }
}

variable "resources" {
  type = object({
    projects = list(object({
      id = number
      name = string
      namespace = string
      deployments = list(object({
        id = number
        name = string
        spec = object({
          container = object({
            image = string
            name = string
            port = object({
              container_port = number
            })
            env = list(object({
              name  = string
              value = any
            }))
          })
        })
      }))
      services = list(object({
        name = string
        spec = object({
          selector = object({
            app = string
          })
          port = object({
            port = number
            target_port = number
          })
        })
      }))
      ingress = list(object({
        name = string
        spec = list(object({
          rule = object({
            host = string
            http = object({
              path = object({
                path = string
                backend = object({
                  service = object({
                    name = string
                    port = object({
                      number = number
                    })
                  })
                })
              })
            })
          })
        }))
      }))
    }))
  })
}

# variable "config" {
#   type = object({
#     clusters = list(object({
#       name = string
#       region = string
#       node_pool = object({
#         pool_name = string
#         size = string
#         node_count = number
#       })
#       projects = list(object({
#         id = number
#         name = string
#         namespace = string
#         deployments = list(object({
#           id = number
#           name = string
#           spec = object({
#             container = object({
#               image = string
#               name = string
#               port = object({
#                 container_port = number
#               })
#               env = list(object({
#                 name  = string
#                 value = any
#               }))
#             })
#           })
#         }))
#         services = list(object({
#           name = string
#           spec = object({
#             selector = object({
#               app = string
#             })
#             port = object({
#               port = number
#               target_port = number
#             })
#           })
#         }))
#         ingress = list(object({
#           name = string
#           spec = list(object({
#             rule = object({
#               host = string
#               http = object({
#                 path = object({
#                   path = string
#                   backend = object({
#                     service = object({
#                       name = string
#                       port = object({
#                         number = number
#                       })
#                     })
#                   })
#                 })
#               })
#             })
#           }))
#         }))
#       }))
#     }))
#   })
# }
