variable "namespace" {
  type = string
}

variable "project" {
  type = string
}

variable "deployments" {
  type = list(object({
    id = number
    name = string
    spec = object({
      container = object({
        image = string
        name = string
        port = object({
          container_port = number
        })
        env = list(object({
          name  = string
          value = string
        }))
      })
    })
  }))
}


resource "kubernetes_deployment" "deployment" {
  count = length(var.deployments)
  metadata {
    name      = "${var.project}-${var.deployments[count.index].name}"
    namespace = var.namespace
  }
  spec {
    replicas = 1
    selector {
      match_labels = {
        app = "${var.project}-${var.deployments[count.index].name}"
      }
    }
    template {
      metadata {
        labels = {
          app = "${var.project}-${var.deployments[count.index].name}"
        }
      }
      spec {
        image_pull_secrets {
          name = "dockerhub-registrykey"
        }
        container {
          image = var.deployments[count.index].spec.container.image
          name  = var.deployments[count.index].spec.container.name
          image_pull_policy = "Always"
          port {
            container_port = var.deployments[count.index].spec.container.port.container_port
          }
          dynamic "env" {
            for_each = {for e in var.deployments[count.index].spec.container.env : e.name => e.value}
            content {
              name = env.key
              value = env.value
            }
          }
          env_from {
            secret_ref {
              name = "${var.project}-${var.deployments[count.index].name}-env-sync"
            }
          }
        }
        #         affinity {
        #           node_affinity {
        #             required_during_scheduling_ignored_during_execution {
        #               node_selector_term {
        #                 match_expressions {
        #                   key = "worker-node"
        #                   operator = "NotIn"
        #                 }
        #               }
        #             }
        #           }
        #         }
        #        node_name = "worker-node-ju2w0"
        #        node_selector = "worker-node-ju2wz"
      }
    }
  }
}