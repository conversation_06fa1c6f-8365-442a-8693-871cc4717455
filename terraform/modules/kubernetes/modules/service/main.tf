variable "namespace" {
  type = string
}
variable "project" {
  type = string
}

variable "services" {
  type = list(object({
    name = string
    spec = object({
      selector = object({
        app = string
      })
      port = object({
        port = number
        target_port = number
      })
    })
  }))
}


resource "kubernetes_service" "service" {
  count = length(var.services)
  metadata {
    name      = "${var.project}-${var.services[count.index].name}"
    namespace = var.namespace
  }
  spec {
    selector = {
      app = "${var.project}-${var.services[count.index].spec.selector.app}"
    }
    #    type = "NodePort"
    port {
      #      node_port   = 30201
      port        = var.services[count.index].spec.port.port
      target_port = var.services[count.index].spec.port.target_port
    }
  }
}