variable "namespace" {
  type = string
}

variable "project" {
  type = string
}

variable "ingress" {
  type = list(object({
    name = string
    spec = list(object({
      rule = object({
        host = string
        http = object({
          path = object({
            path = string
            backend = object({
              service = object({
                name = string
                port = object({
                  number = number
                })
              })
            })
          })
        })
      })
    }))
  }))
}


resource "kubernetes_ingress_v1" "ingress" {
  wait_for_load_balancer = true
  count = length(var.ingress)
  metadata {
    name      = "${var.project}-${var.ingress[count.index].name}"
    namespace = var.namespace
    annotations = {
      "kubernetes.io/ingress.class" = "nginx"
    }
  }
  spec {
    dynamic "rule" {
      for_each = { for r in var.ingress[count.index].spec : r.rule.host => r.rule }
      content {
        host = rule.value["host"]
        http {
          path {
            path = rule.value["http"]["path"]["path"]
            backend {
              service {
                name = "${var.project}-${rule.value["http"]["path"]["backend"]["service"]["name"]}"
                port {
                  number = rule.value["http"]["path"]["backend"]["service"]["port"]["number"]
                }
              }
            }
          }
        }
      }
    }
  }
}