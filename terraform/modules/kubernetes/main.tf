terraform {
  required_providers {
    digitalocean = {
      source  = "digitalocean/digitalocean"
      version = "~> 2.0"
    }
  }
}

provider "digitalocean" {
  token = var.provider_token
}

resource "digitalocean_kubernetes_cluster" "k8s-cluster" {
  # count = terraform.workspace == "default" ? 1 : 0
  count = 1
  name    = var.cluster_configs.cluster_name
  region  = var.cluster_configs.region
  version = "1.31.1-do.5"
  node_pool {
    name = var.cluster_configs.node_pool.pool_name
    size = var.cluster_configs.node_pool.size
    node_count = var.cluster_configs.node_pool.node_count
  }
}

# resource "local_file" "kube-config" {
#   count = terraform.workspace == "default" ? 1 : 0
#   content  = digitalocean_kubernetes_cluster.k8s-cluster[0].kube_config[0].raw_config
#   filename = "./modules/kubernetes/kubeconfig.yaml"
# }

provider "kubernetes" {
  # config_path = "./modules/kubernetes/kubeconfig.yaml"
  host    = digitalocean_kubernetes_cluster.k8s-cluster[0].endpoint
  token   = digitalocean_kubernetes_cluster.k8s-cluster[0].kube_config[0].token
  cluster_ca_certificate = base64decode(
    digitalocean_kubernetes_cluster.k8s-cluster[0].kube_config[0].cluster_ca_certificate
  )
}

resource "kubernetes_namespace" "ingress-nginx" {
  # count = terraform.workspace == "default" ? 1 : 0
  metadata {
    name = "ingress-nginx"
  }
}

resource "kubernetes_namespace" "namespace" {
  count = length(var.resources.projects)
  # count = terraform.workspace == "default" ? 0 : length(var.resources.projects)
  metadata {
    name = var.resources.projects[count.index].namespace
  }
}

provider "helm" {
  kubernetes {
    # config_path = "./modules/kubernetes/kubeconfig.yaml"
    host    = digitalocean_kubernetes_cluster.k8s-cluster[0].endpoint
    token   = digitalocean_kubernetes_cluster.k8s-cluster[0].kube_config[0].token
    cluster_ca_certificate = base64decode(
      digitalocean_kubernetes_cluster.k8s-cluster[0].kube_config[0].cluster_ca_certificate
    )
  }
}


resource "helm_release" "nginx_ingress" {
  # count = terraform.workspace == "default" ? 1 : 0
  name       = "nginx-ingress"
  repository = "https://kubernetes.github.io/ingress-nginx"
  chart      = "ingress-nginx"
  # version    = "4.0.6" # Specify the chart version (optional)
  namespace  = kubernetes_namespace.ingress-nginx.metadata.0.name
  create_namespace = false

  set {
    name  = "controller.service.type"
    value = "LoadBalancer"
  }

  set {
    name  = "controller.service.annotations.service\\.beta\\.kubernetes\\.io/do-loadbalancer-enable-proxy-protocol"
    value = "true"
  }
}

module "deployment_resource" {
  # count = terraform.workspace == "default" ? 0 : length(var.resources.projects)
  count = length(var.resources.projects)
  source = "./modules/deployment"
  project = var.resources.projects[count.index].name
  namespace = var.resources.projects[count.index].namespace
  deployments = var.resources.projects[count.index].deployments
}

module "service_resource" {
  # count = terraform.workspace == "default" ? 0 : length(var.resources.projects)
  count =  length(var.resources.projects)
  source = "./modules/service"
  project = var.resources.projects[count.index].name
  namespace = var.resources.projects[count.index].namespace
  services = var.resources.projects[count.index].services
}

module "ingress_resource" {
  # count = terraform.workspace == "default" ? 0 : length(var.resources.projects)
  count =  length(var.resources.projects)
  source = "./modules/ingress"
  project = var.resources.projects[count.index].name
  namespace = var.resources.projects[count.index].namespace
  ingress = var.resources.projects[count.index].ingress
}