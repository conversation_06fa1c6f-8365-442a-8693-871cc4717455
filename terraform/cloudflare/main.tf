terraform {
  backend "pg" {
    conn_str             = ""
    schema_name          = ""
    skip_schema_creation = false
  }
  required_providers {
    cloudflare = {
      source  = "cloudflare/cloudflare"
      version = "~> 5"
    }
  }
}

provider "cloudflare" {
  api_token = var.cloudflare_api_token
}

data "cloudflare_zones" "get_zones" {
  account = {
    id = var.cloudflare_account_id
  }
}

# Loop through all zones and get DNS records for each zone
data "cloudflare_dns_records" "zone_records" {
  for_each = { for zone in data.cloudflare_zones.get_zones.result : zone.id => zone }
  zone_id  = each.value.id
}

# Output summary with record counts
output "summary" {
  value = {
    total_domains = length(data.cloudflare_zones.get_zones.result)
    zones_with_record_counts = {
      for zone in data.cloudflare_zones.get_zones.result : zone.name => length(data.cloudflare_dns_records.zone_records[zone.id].result)
    }
  }
}

# Output all zones with their DNS records
output "zones_with_dns_records" {
  value = [
    for zone in data.cloudflare_zones.get_zones.result : {
      zone_id = zone.id
      zone_name = zone.name
      zone_status = zone.status
      account = zone.account
      dns_records = [
        for record in data.cloudflare_dns_records.zone_records[zone.id].result : {
          id = record.id
          type = record.type
          name = record.name
          content = record.content
          proxied = record.proxied
        }
      ]
    }
  ]
}

