# Output the load balancer IP address
output "load_balancer_ip" {
  description = "The external IP address of the nginx-ingress load balancer"
  value       = data.kubernetes_service.nginx_ingress_controller.status.0.load_balancer.0.ingress.0.ip
  depends_on  = [helm_release.nginx_ingress]
}

# Data source to get the nginx-ingress controller service
data "kubernetes_service" "nginx_ingress_controller" {
  metadata {
    name      = "${helm_release.nginx_ingress.name}-ingress-nginx-controller"
    namespace = helm_release.nginx_ingress.namespace
  }
  
  depends_on = [helm_release.nginx_ingress]
}

# Additional outputs that might be useful
output "cluster_endpoint" {
  description = "The endpoint for the Kubernetes cluster"
  value       = digitalocean_kubernetes_cluster.k8s-cluster[0].endpoint
}

output "cluster_name" {
  description = "The name of the Kubernetes cluster"
  value       = digitalocean_kubernetes_cluster.k8s-cluster[0].name
}

# output "kubeconfig_path" {
#   description = "Path to the kubeconfig file"
#   value       = local_file.kube-config.filename
# }
