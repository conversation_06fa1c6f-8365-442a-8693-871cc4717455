terraform {
  backend "pg" {
    conn_str             = ""
    schema_name          = ""
    skip_schema_creation = false
  }
  required_providers {
    digitalocean = {
      source  = "digitalocean/digitalocean"
      version = "~> 2.0"
    }
    http = {
      source  = "hashicorp/http"
      version = "3.4.3"
    }
    local = {
      source  = "hashicorp/local"
      version = "2.5.1"
    }
    kubernetes = {
      source  = "hashicorp/kubernetes"
      version = "~> 2.0"
    }
    helm = {
      source  = "hashicorp/helm"
      version = "~> 2.0"
    }
  }
}

provider "digitalocean" {
  # token = local.env_vars["DO_TOKEN"]
  token = var.do_token
}

resource "digitalocean_kubernetes_cluster" "k8s-cluster" {
  count = 1
  name    = jsondecode(data.http.deployments.body)["data"]["clusters"]["name"]
  region  = jsondecode(data.http.deployments.body)["data"]["clusters"]["region"]
  version = "1.33.1-do.2"
  node_pool {
    name = jsondecode(data.http.deployments.body)["data"]["clusters"]["node_pool"]["pool_name"]
    size = jsondecode(data.http.deployments.body)["data"]["clusters"]["node_pool"]["size"]
    node_count = jsondecode(data.http.deployments.body)["data"]["clusters"]["node_pool"]["node_count"]
  }
}

# resource "local_file" "kube-config" {
#   content  = digitalocean_kubernetes_cluster.k8s-cluster[0].kube_config[0].raw_config
#   filename = "./kubeconfig/${jsondecode(data.http.deployments.body)["data"]["clusters"]["name"]}-kubeconfig.yaml"
# }

provider "kubernetes" {
  host    = digitalocean_kubernetes_cluster.k8s-cluster[0].endpoint
  token   = digitalocean_kubernetes_cluster.k8s-cluster[0].kube_config[0].token
  cluster_ca_certificate = base64decode(
    digitalocean_kubernetes_cluster.k8s-cluster[0].kube_config[0].cluster_ca_certificate
  )
}

resource "kubernetes_namespace" "ingress-nginx" {
  metadata {
    name = "ingress-nginx"
  }

  depends_on = [
    digitalocean_kubernetes_cluster.k8s-cluster,
    # local_file.kube-config
  ]
}

provider "helm" {
  kubernetes {
    host                   = digitalocean_kubernetes_cluster.k8s-cluster[0].endpoint
    token                  = digitalocean_kubernetes_cluster.k8s-cluster[0].kube_config[0].token
    cluster_ca_certificate = base64decode(
      digitalocean_kubernetes_cluster.k8s-cluster[0].kube_config[0].cluster_ca_certificate
    )
  }
}

resource "helm_release" "nginx_ingress" {
  name       = jsondecode(data.http.deployments.body)["data"]["clusters"]["name"]
  repository = "https://kubernetes.github.io/ingress-nginx"
  chart      = "ingress-nginx"
  # version    = "4.0.6" # Specify the chart version (optional)
  namespace  = kubernetes_namespace.ingress-nginx.metadata.0.name
  create_namespace = false

  set {
    name  = "controller.service.type"
    value = "LoadBalancer"
  }

  set {
    name  = "controller.service.annotations.service\\.beta\\.kubernetes\\.io/do-loadbalancer-enable-proxy-protocol"
    value = "true"
  }

  depends_on = [
    digitalocean_kubernetes_cluster.k8s-cluster,
    # local_file.kube-config,
    kubernetes_namespace.ingress-nginx
  ]
}

