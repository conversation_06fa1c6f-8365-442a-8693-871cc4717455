variable "operation_endpoint" {
  type = string
  default = ""
}

variable "access_token" {
  type = string
  default = ""
}

variable "do_token" {
  type = string
  default = ""
}

data "http" "deployments" {
  url = var.operation_endpoint
  method = "GET"
  request_headers = {
    Accept = "application/json"
    Authorization = "Bearer ${var.access_token}"
  }
}

# data "local_file" "env" {
#   filename = "../../.env"
# }

# locals {
#   env_vars = {
#     for line in split("\n", data.local_file.env.content) :
#     split("=", line)[0] => split("=", line)[1]
#     if length(split("=", line)) == 2
#   }
# }