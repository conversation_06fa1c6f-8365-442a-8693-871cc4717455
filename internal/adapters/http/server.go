package http

import (
	"context"
	"fmt"
	"log"
	"ops-api/internal/adapters/repository"
	"ops-api/internal/core/ports"
	"ops-api/internal/core/services"

	"gorm.io/gorm"

	"ops-api/config"
	"ops-api/internal/adapters/http/handlers"
	"ops-api/internal/adapters/http/middleware"
	"ops-api/internal/adapters/http/routes"

	"github.com/gofiber/fiber/v2"
)

type Server struct {
	app         *fiber.App
	port        string
	poolService ports.PoolService
}

func NewServer(
	db *gorm.DB,
	cfg *config.Config,
) *Server {
	// Create fiber app
	app := fiber.New()

	// Create auth middleware
	authMiddleware := middleware.NewAuthMiddleware(cfg.JWTSecret)

	// Initialize repositories
	userTypeRepo := repository.NewUserTypeRepository(db)
	userRepo := repository.NewUserRepository(db)
	workspaceRepo := repository.NewWorkspaceRepository(db)
	clusterRepo := repository.NewClusterRepository(db)
	namespaceRepo := repository.NewNamespaceRepository(db)
	domainRepo := repository.NewDomainRepository(db)
	deploymentRepo := repository.NewDeploymentRepository(db)
	environmentRepo := repository.NewEnvironmentRepository(db)
	serviceRepo := repository.NewServiceRepository(db)
	ingressRepo := repository.NewIngressRepository(db)
	ingressSpecRepo := repository.NewIngressSpecRepository(db)
	jobRepo := repository.NewJobRepository(db)
	jobLogRepo := repository.NewJobLogRepository(db)
	serverStatusRepo := repository.NewServerStatusRepository(db)
	orderRepo := repository.NewOrderRepository(db)
	orderDomainRepo := repository.NewOrderDomainRepository(db)
	orderNamespaceRepo := repository.NewOrderNamespaceRepository(db)
	jobStatusRepo := repository.NewJobStatusRepository(db)

	// Initialize shared pool service
	poolService := services.NewPoolService()

	// Initialize services
	userTypeService := services.NewUserTypeService(userTypeRepo)
	userService := services.NewUserService(userRepo, userTypeRepo, cfg.JWTSecret)
	workspaceService := services.NewWorkspaceService(workspaceRepo, userRepo)
	clusterService := services.NewClusterService(clusterRepo, workspaceRepo)
	cloudflareService := services.NewCloudflareService(poolService)
	deploymentService := services.NewDeploymentService(deploymentRepo, namespaceRepo, userRepo)
	environmentService := services.NewEnvironmentService(environmentRepo, deploymentRepo, deploymentService)
	serviceService := services.NewServiceService(serviceRepo, namespaceRepo)
	ingressService := services.NewIngressService(ingressRepo, namespaceRepo)
	ingressSpecService := services.NewIngressSpecService(ingressSpecRepo, serviceRepo, ingressRepo, namespaceRepo, serviceService, ingressService)
	orderService := services.NewOrderService(orderRepo, orderDomainRepo, userRepo, namespaceRepo)
	orderDomainService := services.NewOrderDomainService(orderDomainRepo, orderRepo, orderNamespaceRepo)
	orderNamespaceService := services.NewOrderNamespaceService(orderNamespaceRepo, orderRepo, namespaceRepo, workspaceRepo, clusterRepo)
	namespaceService := services.NewNamespaceService(namespaceRepo, clusterRepo, workspaceRepo, deploymentRepo, serviceRepo, ingressRepo, ingressSpecRepo, domainRepo, orderDomainService)
	dnsService := services.NewDnsService(namespaceService, poolService)
	jobStatusService := services.NewJobStatusService(jobStatusRepo)
	jobService := services.NewJobService(jobRepo, jobLogRepo, jobStatusService)
	jobLogService := services.NewJobLogService(jobLogRepo, jobRepo)
	operationService := services.NewOperationService(clusterRepo, jobService, clusterService, deploymentService, serviceService, ingressService, namespaceService, dnsService, orderService, orderNamespaceService, domainRepo, ingressSpecService, poolService)
	serverStatusService := services.NewServerStatusService(serverStatusRepo)
	domainService := services.NewDomainService(domainRepo, namespaceRepo, clusterRepo, workspaceRepo, orderNamespaceRepo, ingressSpecService, dnsService, operationService, orderDomainService, cloudflareService)
	projectService := services.NewProjectService(namespaceRepo, namespaceService, deploymentService, environmentService, serviceService, ingressService, ingressSpecService, clusterService, orderService, orderNamespaceService, domainService, dnsService, operationService)
	namecheapService := services.NewNamecheapService()
	dashboardService := services.NewDashboardService(userRepo, namespaceRepo, userTypeRepo)

	// Create handlers
	userHandler := handlers.NewUserHandler(userService, userTypeService, cfg.JWTSecret)
	userTypeHandler := handlers.NewUserTypeHandler(userTypeService)
	workspaceHandler := handlers.NewWorkspaceHandler(workspaceService)
	clusterHandler := handlers.NewClusterHandler(clusterService)
	cloudflareHandler := handlers.NewCloudflareHandler(cloudflareService)
	namespaceHandler := handlers.NewNamespaceHandler(namespaceService)
	domainHandler := handlers.NewDomainHandler(domainService, userService)
	deploymentHandler := handlers.NewDeploymentHandler(deploymentService)
	environmentHandler := handlers.NewEnvironmentHandler(environmentService)
	serviceHandler := handlers.NewServiceHandler(serviceService, domainService, namespaceService)
	ingressHandler := handlers.NewIngressHandler(ingressService)
	ingressSpecHandler := handlers.NewIngressSpecHandler(ingressSpecService)
	projectHandler := handlers.NewProjectHandler(projectService)
	jobHandler := handlers.NewJobHandler(jobService)
	jobLogHandler := handlers.NewJobLogHandler(jobLogService)
	operationHandler := handlers.NewOperationHandler(operationService)
	healthHandler := handlers.NewHealthHandler()
	serverStatusHandler := handlers.NewServerStatusHandler(serverStatusService)
	jobStatusHandler := handlers.NewJobStatusHandler(jobStatusService)
	dnsHandler := handlers.NewDnsHandler(dnsService)
	orderHandler := handlers.NewOrderHandler(orderService)
	orderDomainHandler := handlers.NewOrderDomainHandler(orderDomainService)
	orderNamespaceHandler := handlers.NewOrderNamespaceHandler(orderNamespaceService)
	namecheapHandler := handlers.NewNamecheapHandler(namecheapService)
	dashboardHandler := handlers.NewDashboardHandler(dashboardService)

	// Setup router config
	routerConfig := &routes.RouterConfig{
		UserHandler:           userHandler,
		UserTypeHandler:       userTypeHandler,
		WorkspaceHandler:      workspaceHandler,
		ClusterHandler:        clusterHandler,
		CloudflareHandler:     cloudflareHandler,
		NamespaceHandler:      namespaceHandler,
		DomainHandler:         domainHandler,
		DeploymentHandler:     deploymentHandler,
		EnvironmentHandler:    environmentHandler,
		ServiceHandler:        serviceHandler,
		IngressHandler:        ingressHandler,
		IngressSpecHandler:    ingressSpecHandler,
		ProjectHandler:        projectHandler,
		JobHandler:            jobHandler,
		JobLogHandler:         jobLogHandler,
		OperationHandler:      operationHandler,
		HealthHandler:         healthHandler,
		ServerStatusHandler:   serverStatusHandler,
		JobStatusHandler:      jobStatusHandler,
		DnsHandler:            dnsHandler,
		OrderHandler:          orderHandler,
		OrderDomainHandler:    orderDomainHandler,
		OrderNamespaceHandler: orderNamespaceHandler,
		NamecheapHandler:      namecheapHandler,
		DashboardHandler:      dashboardHandler,
		AuthMiddleware:        authMiddleware.AuthRequired(),
		APIKey:                cfg.APIKey,
	}

	// Setup routes
	routes.SetupRoutes(app, routerConfig)

	return &Server{
		app:         app,
		port:        cfg.Port,
		poolService: poolService,
	}
}

func (s *Server) Start() error {
	// Start the shared pool service first
	if err := s.poolService.Start(); err != nil {
		return fmt.Errorf("failed to start pool service: %w", err)
	}

	log.Printf("Server starting on port %s", s.port)
	return s.app.Listen(":" + s.port)
}

func (s *Server) Shutdown(ctx context.Context) error {
	log.Println("Shutting down server...")

	// Stop the pool service first to prevent new tasks
	if err := s.poolService.Stop(); err != nil {
		log.Printf("Warning: failed to stop pool service: %v", err)
	}

	// Create a channel to signal when shutdown is complete
	done := make(chan error, 1)

	// Use the pool service to handle the shutdown task instead of direct goroutine
	shutdownTask := func() {
		done <- s.app.Shutdown()
	}

	// Submit the shutdown task to the pool (if still running) or execute directly
	if s.poolService.IsRunning() {
		if err := s.poolService.Submit(shutdownTask); err != nil {
			// If pool submission fails, execute directly
			go shutdownTask()
		}
	} else {
		// Pool is not running, execute directly
		go shutdownTask()
	}

	// Wait for shutdown to complete or context to timeout
	select {
	case err := <-done:
		return err
	case <-ctx.Done():
		return fmt.Errorf("shutdown timeout: %v", ctx.Err())
	}
}

func (s *Server) GetApp() *fiber.App {
	return s.app
}

func (s *Server) GetPoolService() ports.PoolService {
	return s.poolService
}
