package routes

import (
	"ops-api/internal/adapters/http/handlers"

	"github.com/gofiber/fiber/v2"
)

func RegisterIngressSpecRoutes(router fiber.Router, ingressSpecHandler *handlers.IngressSpecHandler) {
	ingressSpecs := router.Group("/ingress-specs")

	ingressSpecs.Post("/", ingressSpecHandler.CreateIngressSpec)
	ingressSpecs.Get("/", ingressSpecHandler.GetIngressSpecs)
	ingressSpecs.Get("/:id", ingressSpecHandler.GetIngressSpecByID)
	ingressSpecs.Put("/:id", ingressSpecHandler.UpdateIngressSpec)
	ingressSpecs.Delete("/:id", ingressSpecHandler.DeleteIngressSpec)
}
