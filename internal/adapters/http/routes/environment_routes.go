package routes

import (
	"ops-api/internal/adapters/http/handlers"

	"github.com/gofiber/fiber/v2"
)

func RegisterEnvironmentRoutes(router fiber.Router, environmentHandler *handlers.EnvironmentHandler) {
	environments := router.Group("/environments")

	environments.Post("/", environmentHandler.CreateEnvironment)
	environments.Get("/", environmentHandler.GetEnvironments)
	environments.Get("/:id", environmentHandler.GetEnvironmentByID)
	environments.Put("/:id", environmentHandler.UpdateEnvironment)
	environments.Delete("/:id", environmentHandler.DeleteEnvironment)
}
