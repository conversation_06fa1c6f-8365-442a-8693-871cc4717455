package routes

import (
	"ops-api/internal/adapters/http/handlers"

	"github.com/gofiber/fiber/v2"
)

// SetupUserRoutes configures user management endpoints
func SetupUserRoutes(router fiber.Router, userHandler *handlers.UserHandler) {
	users := router.Group("/users")

	// User profile routes
	users.Get("/profile", userHandler.GetProfile)
	users.Put("/profile", userHandler.UpdateProfile)
	users.Put("/change-password", userHandler.ChangePassword)

	// User management routes (admin)
	users.Get("/", userHandler.GetUsers)         // Admin only - list all users
	users.Post("/", userHandler.CreateUser)      // Admin only - create user
	users.Put("/:id", userHandler.UpdateUser)    // Admin only - update user (name and email only, admin/member types only)
	users.Delete("/:id", userHandler.DeleteUser) // Admin only - delete user
}
