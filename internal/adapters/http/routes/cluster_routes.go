package routes

import (
	"ops-api/internal/adapters/http/handlers"

	"github.com/gofiber/fiber/v2"
)

func SetupClusterRoutes(router fiber.Router, clusterHandler *handlers.ClusterHandler) {
	clusters := router.Group("/clusters")

	clusters.Post("/", clusterHandler.CreateCluster)
	clusters.Post("/self", clusterHandler.CreateSelfCluster)
	clusters.Get("/", clusterHandler.GetClusters)
	clusters.Get("/:id", clusterHandler.GetClusterByID)
	clusters.Put("/:id", clusterHandler.UpdateCluster)
	clusters.Delete("/:id", clusterHandler.DeleteCluster)
}
