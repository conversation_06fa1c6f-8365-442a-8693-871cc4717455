package routes

import (
	"ops-api/internal/adapters/http/handlers"

	"github.com/gofiber/fiber/v2"
)

// SetupOrderRoutes configures order management endpoints
func SetupOrderRoutes(router fiber.Router, orderHandler *handlers.OrderHandler) {
	orders := router.Group("/orders")

	// Order CRUD operations
	orders.Post("/", orderHandler.CreateOrder)                         // POST /orders - Create new order with domains
	orders.Get("/", orderHandler.GetOrders)                            // GET /orders - Get all orders with filtering
	orders.Get("/my", orderHandler.GetMyOrders)                        // GET /orders/my - Get current user's orders
	orders.Get("/:id", orderHandler.GetOrderByID)                      // GET /orders/{id} - Get order by ID
	orders.Put("/:id", orderHandler.UpdateOrder)                       // PUT /orders/{id} - Update order
	orders.Patch("/:id/confirm", orderHandler.UpdateOrderConfirmation) // PATCH /orders/{id}/confirm - Update confirmation status
}

// SetupOrderDomainRoutes configures order domain management endpoints
func SetupOrderDomainRoutes(router fiber.Router, orderDomainHandler *handlers.OrderDomainHandler) {
	orderDomains := router.Group("/order-domains")

	// Order Domain operations
	orderDomains.Post("/", orderDomainHandler.CreateOrderDomain)                              // POST /order-domains - Create order domain
	orderDomains.Patch("/:id/availability", orderDomainHandler.UpdateOrderDomainAvailability) // PATCH /order-domains/{id}/availability - Update availability
	orderDomains.Delete("/:id", orderDomainHandler.DeleteOrderDomain)                         // DELETE /order-domains/{id} - Delete order domain
}

// SetupOrderNamespaceRoutes configures order namespace management endpoints
func SetupOrderNamespaceRoutes(router fiber.Router, orderNamespaceHandler *handlers.OrderNamespaceHandler) {
	orderNamespaces := router.Group("/order-namespaces")

	// Order Namespace operations
	orderNamespaces.Post("/", orderNamespaceHandler.CreateOrderNamespace)    // POST /order-namespaces - Create order-namespace association
	orderNamespaces.Get("/", orderNamespaceHandler.GetOrderNamespacesByUser) // GET /order-namespaces - Get by authenticated user
}
