package routes

import (
	"ops-api/internal/adapters/http/handlers"

	"github.com/gofiber/fiber/v2"
)

func SetupNamespaceRoutes(router fiber.Router, namespaceHandler *handlers.NamespaceHandler) {
	namespaces := router.Group("/namespaces")

	namespaces.Get("/types", namespaceHandler.GetNamespaceTypes)
	namespaces.Post("/", namespaceHandler.CreateNamespace)
	namespaces.Get("/", namespaceHandler.GetNamespaces)
	namespaces.Get("/:id", namespaceHandler.GetNamespace)
	namespaces.Put("/:id", namespaceHandler.UpdateNamespace)
	namespaces.Delete("/:id", namespaceHandler.DeleteNamespace)
}

// SetupTemplateRoutes configures template management endpoints
func SetupTemplateRoutes(router fiber.Router, namespaceHandler *handlers.NamespaceHandler) {
	templates := router.Group("/templates")

	// Template operations
	templates.Get("/", namespaceHandler.GetTemplates) // GET /templates - Get all active template namespaces
}
