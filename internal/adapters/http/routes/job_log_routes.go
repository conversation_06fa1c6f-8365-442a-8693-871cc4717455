package routes

import (
	"ops-api/internal/adapters/http/handlers"

	"github.com/gofiber/fiber/v2"
)

// SetupJobLogRoutes configures job log management endpoints
func SetupJobLogRoutes(router fiber.Router, jobLogHandler *handlers.JobLogHandler) {
	jobLogs := router.Group("/job-logs")

	// Job log management routes
	jobLogs.Post("/", jobLogHandler.CreateJobLog)    // Create new job log entry
	jobLogs.Get("/:id", jobLogHandler.GetJobLogByID) // Get specific job log

	// Job-specific logs route (nested under jobs)
	jobs := router.Group("/jobs")
	jobs.Get("/:id/logs", jobLogHandler.GetJobLogsByJobID) // Get logs for specific job
}
