package routes

import (
	"ops-api/internal/adapters/http/handlers"

	"github.com/gofiber/fiber/v2"
)

// SetupJobRoutes configures job management endpoints
func SetupJobRoutes(router fiber.Router, jobHandler *handlers.JobHandler) {
	jobs := router.Group("/jobs")

	// Job management routes
	jobs.Get("/", jobHandler.GetAllJobs)    // List jobs with filtering
	jobs.Post("/", jobHandler.CreateJob)    // Create new job
	jobs.Get("/my", jobHandler.GetMyJobs)   // Get current user's jobs
	jobs.Get("/:id", jobHandler.GetJobByID) // Get specific job details
	jobs.Put("/:id", jobHandler.UpdateJob)  // Update job (owner only)
}
