package routes

import (
	"ops-api/internal/adapters/http/handlers"

	"github.com/gofiber/fiber/v2"
)

func SetupDomainRoutes(router fiber.Router, domainHandler *handlers.DomainHandler) {
	domains := router.Group("/domains")

	domains.Post("/", domainHandler.CreateDomain)
	domains.Get("/", domainHandler.GetDomains)
	domains.Get("/:id", domainHandler.GetDomain)
	domains.Put("/:id", domainHandler.UpdateDomain)
	domains.Patch("/status/:id", domainHandler.UpdateDomainStatus)
	domains.Patch("/default/:id", domainHandler.SetDomainDefault)
	domains.Delete("/:id", domainHandler.DeleteDomain)
}
