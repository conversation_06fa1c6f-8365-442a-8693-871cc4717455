package routes

import (
	"ops-api/internal/adapters/http/handlers"

	"github.com/gofiber/fiber/v2"
)

// SetupJobStatusRoutes sets up job status management routes
func SetupJobStatusRoutes(router fiber.Router, jobStatusHandler *handlers.JobStatusHandler) {
	jobStatus := router.Group("/job-statuses")

	// POST /api/v1/job-statuses - Create new job status
	jobStatus.Post("/", jobStatusHandler.Create)

	// GET /api/v1/job-statuses - Get all job statuses with filtering
	jobStatus.Get("/", jobStatusHandler.GetAll)

	// GET /api/v1/job-statuses/:id - Get job status by ID
	jobStatus.Get("/:id", jobStatusHandler.GetByID)

	// PUT /api/v1/job-statuses/:id - Update job status
	jobStatus.Put("/:id", jobStatusHandler.Update)

	// DELETE /api/v1/job-statuses/:id - Delete job status
	jobStatus.Delete("/:id", jobStatusHandler.Delete)
}
