package routes

import (
	"ops-api/internal/adapters/http/handlers"

	"github.com/gofiber/fiber/v2"
)

func SetupOperationRoutes(router fiber.Router, operationHandler *handlers.OperationHandler) {
	operations := router.Group("/operations")
	operations.Get("/:id", operationHandler.GetOperationData)
	operations.Post("/", operationHandler.CreateOperation)
	operations.Post("/cluster", operationHandler.CreateCluster)
}
