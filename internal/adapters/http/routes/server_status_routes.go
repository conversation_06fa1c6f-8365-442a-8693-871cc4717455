package routes

import (
	"ops-api/internal/adapters/http/handlers"

	"github.com/gofiber/fiber/v2"
)

// SetupServerStatusRoutes sets up server status management routes
func SetupServerStatusRoutes(router fiber.Router, serverStatusHandler *handlers.ServerStatusHandler) {
	serverStatus := router.Group("/server-status")

	// GET /api/v1/server-status - Get all server statuses
	serverStatus.Get("/", serverStatusHandler.GetAll)

	// GET /api/v1/server-status/:id - Get server status by ID
	serverStatus.Get("/:id", serverStatusHandler.GetByID)
}
