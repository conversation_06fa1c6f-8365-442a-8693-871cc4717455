package routes

import (
	"ops-api/internal/adapters/http/handlers"

	"github.com/gofiber/fiber/v2"
)

// SetupNamecheapRoutes configures Namecheap integration endpoints
func SetupNamecheapRoutes(router fiber.Router, namecheapHandler *handlers.NamecheapHandler) {
	namecheap := router.Group("/namecheap")

	// Domain checking endpoint
	namecheap.Post("/check", namecheapHandler.CheckDomains)

	// Domain checking with pricing endpoint
	namecheap.Post("/check-with-pricing", namecheapHandler.CheckDomainsWithPricing)

	// Pricing endpoint
	namecheap.Post("/pricing", namecheapHandler.GetPricing)
}
