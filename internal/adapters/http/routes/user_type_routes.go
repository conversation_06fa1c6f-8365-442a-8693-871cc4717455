package routes

import (
	"ops-api/internal/adapters/http/handlers"

	"github.com/gofiber/fiber/v2"
)

// SetupUserTypeRoutes configures user type management endpoints
func SetupUserTypeRoutes(router fiber.Router, userTypeHandler *handlers.UserTypeHandler) {
	userTypes := router.Group("/user-types")

	// User type management routes
	userTypes.Get("/", userTypeHandler.GetAll)       // Get all user types
	userTypes.Get("/:id", userTypeHandler.GetByID)   // Get specific user type
	userTypes.Post("/", userTypeHandler.Create)      // Admin only - create user type
	userTypes.Put("/:id", userTypeHandler.Update)    // Admin only - update user type
	userTypes.Delete("/:id", userTypeHandler.Delete) // Admin only - delete user type
}
