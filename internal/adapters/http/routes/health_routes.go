package routes

import (
	"ops-api/internal/adapters/http/handlers"

	"github.com/gofiber/fiber/v2"
)

// SetupHealthRoutes configures health check endpoints
func SetupHealthRoutes(app *fiber.App, healthHandler *handlers.HealthHandler) {
	// Health endpoints without API prefix for easy monitoring
	app.Get("/health", healthHandler.Check)
	app.Get("/health/ready", healthHandler.Ready)
	app.Get("/health/live", healthHandler.Live)
}
