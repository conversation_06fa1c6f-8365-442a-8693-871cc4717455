package routes

import (
	"ops-api/internal/adapters/http/handlers"

	"github.com/gofiber/fiber/v2"
)

// SetupWorkspaceRoutes configures workspace management endpoints
func SetupWorkspaceRoutes(router fiber.Router, workspaceHandler *handlers.WorkspaceHandler) {
	workspaces := router.Group("/workspaces")

	// Workspace CRUD routes (authentication is already applied at the protected router level)
	workspaces.Post("/", workspaceHandler.CreateWorkspace)      // Create workspace
	workspaces.Get("/", workspaceHandler.GetWorkspaces)         // Get all user's workspaces (with filtering)
	workspaces.Get("/:id", workspaceHandler.GetWorkspaceByID)   // Get workspace by ID
	workspaces.Put("/:id", workspaceHandler.UpdateWorkspace)    // Update workspace
	workspaces.Delete("/:id", workspaceHandler.DeleteWorkspace) // Delete workspace
}
