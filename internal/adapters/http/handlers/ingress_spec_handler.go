package handlers

import (
	"strconv"
	"strings"

	"ops-api/internal/core/dto"
	"ops-api/internal/core/ports"
	"ops-api/pkg/utils/response"

	"github.com/gofiber/fiber/v2"
)

type IngressSpecHandler struct {
	ingressSpecService ports.IngressSpecService
}

func NewIngressSpecHandler(ingressSpecService ports.IngressSpecService) *IngressSpecHandler {
	return &IngressSpecHandler{
		ingressSpecService: ingressSpecService,
	}
}

func (h *IngressSpecHandler) CreateIngressSpec(c *fiber.Ctx) error {
	var req dto.CreateIngressSpecRequest
	if err := c.Body<PERSON>er(&req); err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid request format")
	}

	ingressSpec, err := h.ingressSpecService.Create(req.Host, req.Path, req.Port, req.ServiceID, req.IngressID)
	if err != nil {
		if strings.Contains(err.Error(), "access denied") {
			return response.Error(c, fiber.StatusForbidden, err.Error())
		}
		return response.Error(c, fiber.StatusBadRequest, err.Error())
	}

	return response.Success(c, fiber.StatusCreated, "Ingress spec created successfully", dto.ToIngressSpecDetailDTO(ingressSpec))
}

func (h *IngressSpecHandler) GetIngressSpecs(c *fiber.Ctx) error {
	filter := &ports.IngressSpecFilter{}

	if host := c.Query("host"); host != "" {
		filter.Host = &host
	}

	if path := c.Query("path"); path != "" {
		filter.Path = &path
	}

	if serviceIDStr := c.Query("service_id"); serviceIDStr != "" {
		serviceID, err := strconv.ParseUint(serviceIDStr, 10, 64)
		if err != nil {
			return response.Error(c, fiber.StatusBadRequest, "Invalid service_id parameter")
		}
		filter.ServiceID = &serviceID
	}

	if ingressIDStr := c.Query("ingress_id"); ingressIDStr != "" {
		ingressID, err := strconv.ParseUint(ingressIDStr, 10, 64)
		if err != nil {
			return response.Error(c, fiber.StatusBadRequest, "Invalid ingress_id parameter")
		}
		filter.IngressID = &ingressID
	}

	ingressSpecs, err := h.ingressSpecService.GetAll(filter)
	if err != nil {
		return response.Error(c, fiber.StatusInternalServerError, err.Error())
	}

	var ingressSpecDTOs []*dto.IngressSpecListItemResponse
	for _, ingressSpec := range ingressSpecs {
		ingressSpecDTOs = append(ingressSpecDTOs, dto.ToIngressSpecListItemDTO(ingressSpec))
	}

	return response.Success(c, fiber.StatusOK, "Ingress specs retrieved successfully", ingressSpecDTOs)
}

func (h *IngressSpecHandler) GetIngressSpecByID(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid ID parameter")
	}

	ingressSpec, err := h.ingressSpecService.GetByID(id)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return response.Error(c, fiber.StatusNotFound, err.Error())
		}
		return response.Error(c, fiber.StatusInternalServerError, err.Error())
	}

	return response.Success(c, fiber.StatusOK, "Ingress spec retrieved successfully", dto.ToIngressSpecDetailDTO(ingressSpec))
}

func (h *IngressSpecHandler) UpdateIngressSpec(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid ID parameter")
	}

	var req dto.UpdateIngressSpecRequest
	if err := c.BodyParser(&req); err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid request format")
	}

	ingressSpec, err := h.ingressSpecService.Update(id, req.Host, req.Path, req.Port, req.ServiceID)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return response.Error(c, fiber.StatusNotFound, err.Error())
		}
		if strings.Contains(err.Error(), "access denied") {
			return response.Error(c, fiber.StatusForbidden, err.Error())
		}
		return response.Error(c, fiber.StatusBadRequest, err.Error())
	}

	return response.Success(c, fiber.StatusOK, "Ingress spec updated successfully", dto.ToIngressSpecDetailDTO(ingressSpec))
}

func (h *IngressSpecHandler) DeleteIngressSpec(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid ID parameter")
	}

	err = h.ingressSpecService.Delete(id)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return response.Error(c, fiber.StatusNotFound, err.Error())
		}
		if strings.Contains(err.Error(), "access denied") {
			return response.Error(c, fiber.StatusForbidden, err.Error())
		}
		return response.Error(c, fiber.StatusInternalServerError, err.Error())
	}

	return response.Success(c, fiber.StatusOK, "Ingress spec deleted successfully", nil)
}
