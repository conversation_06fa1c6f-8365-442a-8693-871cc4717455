package handlers

import (
	"strconv"
	"strings"

	"ops-api/internal/core/dto"
	"ops-api/internal/core/ports"
	"ops-api/pkg/utils/response"

	"github.com/gofiber/fiber/v2"
)

type JobLogHandler struct {
	jobLogService ports.JobLogService
}

func NewJobLogHandler(jobLogService ports.JobLogService) *JobLogHandler {
	return &JobLogHandler{
		jobLogService: jobLogService,
	}
}

// GetJobLogsByJobID godoc
// @Summary Get job logs by job ID
// @Description Get all job logs for a specific job
// @Tags job-logs
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param job_id path int true "Job ID"
// @Success 200 {array} dto.JobLogListItemResponse
// @Failure 400 {object} errs.ErrorResponse
// @Failure 401 {object} errs.ErrorResponse
// @Failure 500 {object} errs.ErrorResponse
// @Router /jobs/{job_id}/logs [get]
func (h *JobLogHandler) GetJobLogsByJobID(c *fiber.Ctx) error {
	jobIDStr := c.Params("job_id")
	jobID, err := strconv.ParseUint(jobIDStr, 10, 64)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid job ID")
	}

	jobLogs, err := h.jobLogService.GetJobLogsByJobID(jobID)
	if err != nil {
		if strings.Contains(err.Error(), "required") {
			return response.Error(c, fiber.StatusBadRequest, err.Error())
		}
		return response.Error(c, fiber.StatusInternalServerError, "Failed to fetch job logs")
	}

	jobLogList := make([]*dto.JobLogListItemResponse, len(jobLogs))
	for i, jobLog := range jobLogs {
		jobLogList[i] = dto.ToJobLogListItemDTO(jobLog)
	}

	return response.Success(c, fiber.StatusOK, "Job logs retrieved successfully", jobLogList)
}

// GetJobLogByID godoc
// @Summary Get job log by ID
// @Description Get a specific job log by its ID
// @Tags job-logs
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "Job Log ID"
// @Success 200 {object} dto.JobLogDetailResponse
// @Failure 400 {object} errs.ErrorResponse
// @Failure 401 {object} errs.ErrorResponse
// @Failure 404 {object} errs.ErrorResponse
// @Failure 500 {object} errs.ErrorResponse
// @Router /job-logs/{id} [get]
func (h *JobLogHandler) GetJobLogByID(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid job log ID")
	}

	jobLog, err := h.jobLogService.GetJobLogByID(id)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return response.Error(c, fiber.StatusNotFound, "Job log not found")
		}
		return response.Error(c, fiber.StatusInternalServerError, "Failed to fetch job log")
	}

	responseData := dto.ToJobLogDetailDTO(jobLog)
	return response.Success(c, fiber.StatusOK, "Job log retrieved successfully", responseData)
}

// CreateJobLog godoc
// @Summary Create a new job log
// @Description Create a new job log entry for a specific job
// @Tags job-logs
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param job_id path int true "Job ID"
// @Param request body dto.CreateJobLogRequest true "Create job log request"
// @Success 201 {object} dto.JobLogDetailResponse
// @Failure 400 {object} errs.ErrorResponse
// @Failure 401 {object} errs.ErrorResponse
// @Failure 500 {object} errs.ErrorResponse
// @Router /jobs/{job_id}/logs [post]
func (h *JobLogHandler) CreateJobLog(c *fiber.Ctx) error {
	jobIDStr := c.Params("job_id")
	jobID, err := strconv.ParseUint(jobIDStr, 10, 64)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid job ID")
	}

	var req dto.CreateJobLogRequest
	if err := c.BodyParser(&req); err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid request format")
	}

	jobLog, err := h.jobLogService.CreateJobLog(jobID, req.Name, req.Description)
	if err != nil {
		if strings.Contains(err.Error(), "required") || strings.Contains(err.Error(), "not found") {
			return response.Error(c, fiber.StatusBadRequest, err.Error())
		}
		return response.Error(c, fiber.StatusInternalServerError, "Failed to create job log")
	}

	responseData := dto.ToJobLogDetailDTO(jobLog)
	return response.Success(c, fiber.StatusCreated, "Job log created successfully", responseData)
}
