package handlers

import (
	"ops-api/internal/core/domain"
	"strconv"
	"strings"

	"ops-api/internal/core/dto"
	"ops-api/internal/core/ports"
	"ops-api/pkg/utils/response"

	"github.com/gofiber/fiber/v2"
)

type JobHandler struct {
	jobService ports.JobService
}

func NewJobHandler(jobService ports.JobService) *JobHandler {
	return &JobHandler{
		jobService: jobService,
	}
}

// CreateJob godoc
// @Summary Create a new job
// @Description Create a new job with automatic user ID assignment from auth token
// @Tags jobs
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body dto.CreateJobRequest true "Create job request"
// @Success 201 {object} dto.JobDetailResponse
// @Failure 400 {object} errs.ErrorResponse
// @Failure 401 {object} errs.ErrorResponse
// @Failure 500 {object} errs.ErrorResponse
// @Router /jobs [post]
func (h *JobHandler) CreateJob(c *fiber.Ctx) error {
	var req dto.CreateJobRequest
	if err := c.BodyParser(&req); err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid request format")
	}

	// Get user ID from auth context
	userIDInterface := c.Locals("user_id")
	if userIDInterface == nil {
		return response.Error(c, fiber.StatusUnauthorized, "User not authenticated")
	}

	var userID uint64
	switch v := userIDInterface.(type) {
	case uint:
		userID = uint64(v)
	case uint64:
		userID = v
	case float64:
		userID = uint64(v)
	default:
		return response.Error(c, fiber.StatusUnauthorized, "Invalid user ID format")
	}

	job, err := h.jobService.CreateJob(userID, req.Name, req.Description, req.JobStatusID, req.EventID, req.Event, req.Action)
	if err != nil {
		if strings.Contains(err.Error(), "required") {
			return response.Error(c, fiber.StatusBadRequest, err.Error())
		}
		return response.Error(c, fiber.StatusInternalServerError, "Failed to create job")
	}

	jobResponse := dto.ToJobDetailDTO(job)
	return response.Success(c, fiber.StatusCreated, "Job created successfully", jobResponse)
}

// GetAllJobs godoc
// @Summary Get all jobs
// @Description Get all jobs with optional filters
// @Tags jobs
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param my_jobs query bool false "Filter for current user's jobs only"
// @Param name query string false "Filter by job name"
// @Param job_status_id query int false "Filter by job status ID"
// @Param event_id query int false "Filter by event ID"
// @Param event query string false "Filter by event"
// @Param action query string false "Filter by action"
// @Success 200 {array} dto.JobListItemResponse
// @Failure 401 {object} errs.ErrorResponse
// @Failure 500 {object} errs.ErrorResponse
// @Router /jobs [get]
func (h *JobHandler) GetAllJobs(c *fiber.Ctx) error {
	filter := &ports.JobFilter{}

	// Check if user wants only their jobs
	myJobsParam := c.Query("my_jobs")
	if myJobsParam == "true" {
		userIDInterface := c.Locals("user_id")
		if userIDInterface == nil {
			return response.Error(c, fiber.StatusUnauthorized, "User not authenticated")
		}

		var userID uint64
		switch v := userIDInterface.(type) {
		case uint:
			userID = uint64(v)
		case uint64:
			userID = v
		case float64:
			userID = uint64(v)
		default:
			return response.Error(c, fiber.StatusUnauthorized, "Invalid user ID format")
		}
		filter.UserID = &userID
	}

	// Apply other filters
	if name := c.Query("name"); name != "" {
		filter.Name = &name
	}

	if jobStatusIDStr := c.Query("job_status_id"); jobStatusIDStr != "" {
		if jobStatusID, err := strconv.ParseUint(jobStatusIDStr, 10, 64); err == nil {
			filter.JobStatusID = &jobStatusID
		}
	}

	if eventIDStr := c.Query("event_id"); eventIDStr != "" {
		if eventID, err := strconv.ParseUint(eventIDStr, 10, 64); err == nil {
			filter.EventID = &eventID
		}
	}

	if event := c.Query("event"); event != "" {
		jobEvent := domain.JobEvent(event)
		filter.Event = &jobEvent
	}

	if action := c.Query("action"); action != "" {
		jobAction := domain.JobAction(action)
		filter.Action = &jobAction
	}

	jobs, err := h.jobService.GetAllJobs(filter)
	if err != nil {
		return response.Error(c, fiber.StatusInternalServerError, "Failed to fetch jobs")
	}

	jobList := make([]*dto.JobListItemResponse, len(jobs))
	for i, job := range jobs {
		jobList[i] = dto.ToJobListItemDTO(job)
	}

	return response.Success(c, fiber.StatusOK, "Jobs retrieved successfully", jobList)
}

// GetMyJobs godoc
// @Summary Get current user's jobs
// @Description Get all jobs created by the current authenticated user
// @Tags jobs
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {array} dto.JobListItemResponse
// @Failure 401 {object} errs.ErrorResponse
// @Failure 500 {object} errs.ErrorResponse
// @Router /jobs/my [get]
func (h *JobHandler) GetMyJobs(c *fiber.Ctx) error {
	userIDInterface := c.Locals("user_id")
	if userIDInterface == nil {
		return response.Error(c, fiber.StatusUnauthorized, "User not authenticated")
	}

	var userID uint64
	switch v := userIDInterface.(type) {
	case uint:
		userID = uint64(v)
	case uint64:
		userID = v
	case float64:
		userID = uint64(v)
	default:
		return response.Error(c, fiber.StatusUnauthorized, "Invalid user ID format")
	}

	jobs, err := h.jobService.GetMyJobs(userID)
	if err != nil {
		return response.Error(c, fiber.StatusInternalServerError, "Failed to fetch user jobs")
	}

	jobList := make([]*dto.JobListItemResponse, len(jobs))
	for i, job := range jobs {
		jobList[i] = dto.ToJobListItemDTO(job)
	}

	return response.Success(c, fiber.StatusOK, "User jobs retrieved successfully", jobList)
}

// GetJobByID godoc
// @Summary Get job by ID
// @Description Get a specific job by its ID
// @Tags jobs
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "Job ID"
// @Success 200 {object} dto.JobDetailResponse
// @Failure 400 {object} errs.ErrorResponse
// @Failure 401 {object} errs.ErrorResponse
// @Failure 404 {object} errs.ErrorResponse
// @Failure 500 {object} errs.ErrorResponse
// @Router /jobs/{id} [get]
func (h *JobHandler) GetJobByID(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid job ID")
	}

	job, err := h.jobService.GetJobByID(id)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return response.Error(c, fiber.StatusNotFound, "Job not found")
		}
		return response.Error(c, fiber.StatusInternalServerError, "Failed to fetch job")
	}

	jobResponse := dto.ToJobDetailDTO(job)
	return response.Success(c, fiber.StatusOK, "Job retrieved successfully", jobResponse)
}

// UpdateJob godoc
// @Summary Update an existing job
// @Description Update job details. Users can only update their own jobs unless they are admin.
// @Tags jobs
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "Job ID"
// @Param request body dto.UpdateJobRequest true "Update job request"
// @Success 200 {object} dto.JobDetailResponse
// @Failure 400 {object} errs.ErrorResponse
// @Failure 401 {object} errs.ErrorResponse
// @Failure 403 {object} errs.ErrorResponse
// @Failure 404 {object} errs.ErrorResponse
// @Failure 500 {object} errs.ErrorResponse
// @Router /jobs/{id} [put]
func (h *JobHandler) UpdateJob(c *fiber.Ctx) error {
	// Parse job ID from URL parameter
	jobIDStr := c.Params("id")
	jobID, err := strconv.ParseUint(jobIDStr, 10, 64)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid job ID")
	}

	var req dto.UpdateJobRequest
	if err := c.BodyParser(&req); err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid request format")
	}

	// Get user ID from auth context
	userIDInterface := c.Locals("user_id")
	if userIDInterface == nil {
		return response.Error(c, fiber.StatusUnauthorized, "User not authenticated")
	}

	var userID uint64
	switch v := userIDInterface.(type) {
	case uint:
		userID = uint64(v)
	case uint64:
		userID = v
	case float64:
		userID = uint64(v)
	default:
		return response.Error(c, fiber.StatusUnauthorized, "Invalid user ID format")
	}

	// Update the job
	job, err := h.jobService.UpdateJob(jobID, userID, req.Name, req.Description, req.JobStatusID, req.EventID, req.Event, req.Action)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return response.Error(c, fiber.StatusNotFound, "Job not found")
		}
		if strings.Contains(err.Error(), "unauthorized") {
			return response.Error(c, fiber.StatusForbidden, "You are not authorized to update this job")
		}
		if strings.Contains(err.Error(), "required") {
			return response.Error(c, fiber.StatusBadRequest, err.Error())
		}
		return response.Error(c, fiber.StatusInternalServerError, "Failed to update job")
	}

	jobResponse := dto.ToJobDetailDTO(job)
	return response.Success(c, fiber.StatusOK, "Job updated successfully", jobResponse)
}
