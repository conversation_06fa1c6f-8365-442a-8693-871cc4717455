package handlers

import (
	"ops-api/internal/core/ports"
	"ops-api/pkg/utils/response"

	"github.com/gofiber/fiber/v2"
)

type DashboardHandler struct {
	dashboardService ports.DashboardService
}

func NewDashboardHandler(dashboardService ports.DashboardService) *DashboardHandler {
	return &DashboardHandler{
		dashboardService: dashboardService,
	}
}

// GetAnalytics returns dashboard analytics data
func (h *DashboardHandler) GetAnalytics(c *fiber.Ctx) error {
	analytics, err := h.dashboardService.GetAnalytics()
	if err != nil {
		return response.Error(c, fiber.StatusInternalServerError, "Failed to get dashboard analytics")
	}

	return response.Success(c, fiber.StatusOK, "Dashboard analytics retrieved successfully", analytics)
}
