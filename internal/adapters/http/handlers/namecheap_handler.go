package handlers

import (
	"ops-api/internal/core/dto"
	"ops-api/internal/core/ports"
	"ops-api/pkg/utils/response"

	"github.com/gofiber/fiber/v2"
)

type NamecheapHandler struct {
	namecheapService ports.NamecheapService
}

func NewNamecheapHandler(namecheapService ports.NamecheapService) *NamecheapHandler {
	return &NamecheapHandler{
		namecheapService: namecheapService,
	}
}

// CheckDomains handles POST /namecheap/check endpoint - now enhanced with pricing information
func (h *NamecheapHandler) CheckDomains(c *fiber.Ctx) error {
	var req dto.NamecheapCheckRequest

	// Parse request body
	if err := c.BodyParser(&req); err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid request format")
	}

	// Validate request
	if len(req.Domains) == 0 {
		return response.Error(c, fiber.StatusBadRequest, "Domains array cannot be empty")
	}

	// Validate individual domains
	for i, domain := range req.Domains {
		if domain == "" {
			return response.Error(c, fiber.StatusBadRequest, "Domain at index "+string(rune(i))+" cannot be empty")
		}
	}

	// Call service to check domains with pricing (enhanced functionality)
	result, err := h.namecheapService.CheckDomainsWithPricing(req.Domains)
	if err != nil {
		return response.Error(c, fiber.StatusInternalServerError, err.Error())
	}

	return response.Success(c, fiber.StatusOK, "Domain check completed successfully", result)
}

// CheckDomainsWithPricing handles POST /namecheap/check-with-pricing endpoint
func (h *NamecheapHandler) CheckDomainsWithPricing(c *fiber.Ctx) error {
	var req dto.NamecheapCheckRequest

	// Parse request body
	if err := c.BodyParser(&req); err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid request format")
	}

	// Validate request
	if len(req.Domains) == 0 {
		return response.Error(c, fiber.StatusBadRequest, "Domains array cannot be empty")
	}

	// Validate individual domains
	for i, domain := range req.Domains {
		if domain == "" {
			return response.Error(c, fiber.StatusBadRequest, "Domain at index "+string(rune(i))+" cannot be empty")
		}
	}

	// Call service to check domains with pricing
	result, err := h.namecheapService.CheckDomainsWithPricing(req.Domains)
	if err != nil {
		return response.Error(c, fiber.StatusInternalServerError, err.Error())
	}

	return response.Success(c, fiber.StatusOK, "Domain check with pricing completed successfully", result)
}

// GetPricing handles POST /namecheap/pricing endpoint
func (h *NamecheapHandler) GetPricing(c *fiber.Ctx) error {
	var req dto.NamecheapPricingRequest

	// Parse request body
	if err := c.BodyParser(&req); err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid request format")
	}

	// Validate request
	if req.ProductName == "" {
		return response.Error(c, fiber.StatusBadRequest, "Product name cannot be empty")
	}

	// Call service to get pricing
	result, err := h.namecheapService.GetPricing(req.ProductName)
	if err != nil {
		return response.Error(c, fiber.StatusInternalServerError, err.Error())
	}

	return response.Success(c, fiber.StatusOK, "Pricing retrieved successfully", result)
}
