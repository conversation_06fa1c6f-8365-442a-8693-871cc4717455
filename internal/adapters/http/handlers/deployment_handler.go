package handlers

import (
	"fmt"
	"strconv"
	"strings"

	"ops-api/internal/core/dto"
	"ops-api/internal/core/ports"
	"ops-api/pkg/utils/response"

	"github.com/gofiber/fiber/v2"
)

type DeploymentHandler struct {
	deploymentService ports.DeploymentService
}

func NewDeploymentHandler(deploymentService ports.DeploymentService) *DeploymentHandler {
	return &DeploymentHandler{
		deploymentService: deploymentService,
	}
}

func (h *DeploymentHandler) CreateDeployment(c *fiber.Ctx) error {
	var req dto.CreateDeploymentRequest
	if err := c.BodyParser(&req); err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid request format")
	}

	deployment, err := h.deploymentService.Create(req.Name, req.Image, req.ContainerPort, req.Replicas, req.NamespaceID)
	fmt.Println(deployment)
	if err != nil {
		if strings.Contains(err.Error(), "access denied") {
			return response.Error(c, fiber.StatusForbidden, err.Error())
		}
		return response.Error(c, fiber.StatusBadRequest, err.Error())
	}

	return response.Success(c, fiber.StatusCreated, "Deployment created successfully", dto.ToDeploymentDetailDTO(deployment))
}

func (h *DeploymentHandler) GetDeployments(c *fiber.Ctx) error {

	filter := &ports.DeploymentFilter{}

	if name := c.Query("name"); name != "" {
		filter.Name = &name
	}

	if namespaceIDStr := c.Query("namespace_id"); namespaceIDStr != "" {
		namespaceID, err := strconv.ParseUint(namespaceIDStr, 10, 64)
		if err != nil {
			return response.Error(c, fiber.StatusBadRequest, "Invalid namespace_id parameter")
		}
		filter.NamespaceID = &namespaceID
	}

	deployments, err := h.deploymentService.GetAll(filter)
	if err != nil {
		return response.Error(c, fiber.StatusInternalServerError, err.Error())
	}

	var deploymentDTOs []*dto.DeploymentListItemResponse
	for _, deployment := range deployments {
		deploymentDTOs = append(deploymentDTOs, dto.ToDeploymentListItemDTO(deployment))
	}

	return response.Success(c, fiber.StatusOK, "Deployments retrieved successfully", deploymentDTOs)
}

func (h *DeploymentHandler) GetDeploymentByID(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid ID parameter")
	}

	deployment, err := h.deploymentService.GetByID(id)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return response.Error(c, fiber.StatusNotFound, "Deployment not found")
		}
		return response.Error(c, fiber.StatusInternalServerError, err.Error())
	}

	return response.Success(c, fiber.StatusOK, "Deployment retrieved successfully", dto.ToDeploymentDetailDTO(deployment))
}

func (h *DeploymentHandler) UpdateDeployment(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid ID parameter")
	}

	var req dto.UpdateDeploymentRequest
	if err := c.BodyParser(&req); err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid request format")
	}

	deployment, err := h.deploymentService.Update(id, req.Name, req.Image, req.ContainerPort, req.Replicas, req.NamespaceID, req.StatusID)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return response.Error(c, fiber.StatusNotFound, err.Error())
		}
		return response.Error(c, fiber.StatusBadRequest, err.Error())
	}

	return response.Success(c, fiber.StatusOK, "Deployment updated successfully", dto.ToDeploymentDetailDTO(deployment))
}

func (h *DeploymentHandler) UpdateDeploymentStatus(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid ID parameter")
	}

	var req dto.UpdateDeploymentStatusRequest
	if err := c.BodyParser(&req); err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid request format")
	}

	deployment, err := h.deploymentService.UpdateStatus(id, req.StatusID)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return response.Error(c, fiber.StatusNotFound, err.Error())
		}
		return response.Error(c, fiber.StatusBadRequest, err.Error())
	}

	return response.Success(c, fiber.StatusOK, "Deployment status updated successfully", dto.ToDeploymentDetailDTO(deployment))
}

func (h *DeploymentHandler) DeleteDeployment(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid ID parameter")
	}

	err = h.deploymentService.Delete(id)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return response.Error(c, fiber.StatusNotFound, "Deployment not found")
		}
		return response.Error(c, fiber.StatusInternalServerError, err.Error())
	}

	return response.Success(c, fiber.StatusOK, "Deployment deleted successfully", nil)
}
