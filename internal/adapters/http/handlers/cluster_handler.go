package handlers

import (
	"strconv"
	"strings"

	"ops-api/internal/core/dto"
	"ops-api/internal/core/ports"
	"ops-api/pkg/utils/response"

	"github.com/gofiber/fiber/v2"
)

type ClusterHandler struct {
	clusterService ports.ClusterService
}

func NewClusterHandler(clusterService ports.ClusterService) *ClusterHandler {
	return &ClusterHandler{
		clusterService: clusterService,
	}
}

func (h *ClusterHandler) CreateCluster(c *fiber.Ctx) error {
	userID := c.Locals("user_id").(uint64)

	var req dto.CreateClusterRequest
	if err := c.BodyParser(&req); err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid request format")
	}

	cluster, err := h.clusterService.CreateWithUserValidation(userID, req.Name, req.Region, req.PoolName, req.Size, req.NodeCount, req.WorkspaceID)
	if err != nil {
		if strings.Contains(err.<PERSON>rror(), "access denied") {
			return response.Error(c, fiber.StatusForbidden, err.Error())
		}
		return response.Error(c, fiber.StatusBadRequest, err.Error())
	}

	return response.Success(c, fiber.StatusCreated, "Cluster created successfully", dto.ToClusterDetailDTO(cluster))
}

func (h *ClusterHandler) CreateSelfCluster(c *fiber.Ctx) error {
	userID := c.Locals("user_id").(uint64)

	var req dto.CreateSelfClusterRequest
	if err := c.BodyParser(&req); err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid request format")
	}

	cluster, err := h.clusterService.CreateSelfCluster(userID, req.WorkspaceID)
	if err != nil {
		if strings.Contains(err.Error(), "access denied") {
			return response.Error(c, fiber.StatusForbidden, err.Error())
		}
		return response.Error(c, fiber.StatusBadRequest, err.Error())
	}

	return response.Success(c, fiber.StatusCreated, "Self cluster created successfully", dto.ToClusterDetailDTO(cluster))
}

func (h *ClusterHandler) GetClusters(c *fiber.Ctx) error {
	userID := c.Locals("user_id").(uint64)

	filter := &ports.ClusterFilter{}

	// Apply name filter if provided
	if name := c.Query("name"); name != "" {
		filter.Name = &name
	}

	// Apply region filter if provided
	if region := c.Query("region"); region != "" {
		filter.Region = &region
	}

	// Apply workspace_id filter if provided
	if workspaceIDStr := c.Query("workspace_id"); workspaceIDStr != "" {
		workspaceID, err := strconv.ParseUint(workspaceIDStr, 10, 64)
		if err != nil {
			return response.Error(c, fiber.StatusBadRequest, "Invalid workspace_id parameter")
		}
		filter.WorkspaceID = &workspaceID
	}

	// Get clusters only from user's owned workspaces
	clusters, err := h.clusterService.GetAllByUserWorkspaces(userID, filter)
	if err != nil {
		return response.Error(c, fiber.StatusInternalServerError, "Failed to fetch clusters")
	}

	var clusterResponses []*dto.ClusterListItemResponse
	for _, cluster := range clusters {
		clusterResponses = append(clusterResponses, dto.ToClusterListItemDTO(cluster))
	}

	return response.Success(c, fiber.StatusOK, "Clusters retrieved successfully", clusterResponses)
}

func (h *ClusterHandler) GetClusterByID(c *fiber.Ctx) error {
	userID := c.Locals("user_id").(uint64)

	idStr := c.Params("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid cluster ID")
	}

	cluster, err := h.clusterService.GetByID(id)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return response.Error(c, fiber.StatusNotFound, "Cluster not found")
		}
		return response.Error(c, fiber.StatusInternalServerError, "Failed to fetch cluster")
	}

	// Check if cluster belongs to a workspace owned by the user
	if cluster.Workspace != nil && cluster.Workspace.UserID != userID {
		return response.Error(c, fiber.StatusForbidden, "Access denied: you can only view clusters from your own workspaces")
	}

	return response.Success(c, fiber.StatusOK, "Cluster retrieved successfully", dto.ToClusterDetailDTO(cluster))
}

func (h *ClusterHandler) UpdateCluster(c *fiber.Ctx) error {
	userID := c.Locals("user_id").(uint64)

	idStr := c.Params("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid cluster ID")
	}

	var req dto.UpdateClusterRequest
	if err := c.BodyParser(&req); err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid request format")
	}

	// First check if cluster exists and user has access
	existingCluster, err := h.clusterService.GetByID(id)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return response.Error(c, fiber.StatusNotFound, "Cluster not found")
		}
		return response.Error(c, fiber.StatusInternalServerError, "Failed to fetch cluster")
	}

	// Check if cluster belongs to a workspace owned by the user
	if existingCluster.Workspace != nil && existingCluster.Workspace.UserID != userID {
		return response.Error(c, fiber.StatusForbidden, "Access denied: you can only update clusters from your own workspaces")
	}

	cluster, err := h.clusterService.Update(id, req.Name, req.Region, req.PoolName, req.Size, req.NodeCount, req.WorkspaceID, req.StatusID)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return response.Error(c, fiber.StatusNotFound, "Cluster not found")
		}
		return response.Error(c, fiber.StatusBadRequest, err.Error())
	}

	return response.Success(c, fiber.StatusOK, "Cluster updated successfully", dto.ToClusterRelationDTO(cluster))
}

func (h *ClusterHandler) DeleteCluster(c *fiber.Ctx) error {
	userID := c.Locals("user_id").(uint64)

	idStr := c.Params("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid cluster ID")
	}

	// First check if cluster exists and user has access
	existingCluster, err := h.clusterService.GetByID(id)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return response.Error(c, fiber.StatusNotFound, "Cluster not found")
		}
		return response.Error(c, fiber.StatusInternalServerError, "Failed to fetch cluster")
	}

	// Check if cluster belongs to a workspace owned by the user
	if existingCluster.Workspace != nil && existingCluster.Workspace.UserID != userID {
		return response.Error(c, fiber.StatusForbidden, "Access denied: you can only delete clusters from your own workspaces")
	}

	err = h.clusterService.Delete(id)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return response.Error(c, fiber.StatusNotFound, "Cluster not found")
		}
		return response.Error(c, fiber.StatusInternalServerError, "Failed to delete cluster")
	}

	return response.Success(c, fiber.StatusOK, "Cluster deleted successfully", nil)
}
