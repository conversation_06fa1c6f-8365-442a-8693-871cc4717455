package handlers

import (
	"strconv"

	"ops-api/internal/core/dto"
	"ops-api/internal/core/ports"
	"ops-api/pkg/utils/response"

	"github.com/gofiber/fiber/v2"
)

type ServerStatusHandler struct {
	serverStatusService ports.ServerStatusService
}

func NewServerStatusHandler(serverStatusService ports.ServerStatusService) *ServerStatusHandler {
	return &ServerStatusHandler{
		serverStatusService: serverStatusService,
	}
}

func (h *ServerStatusHandler) GetAll(c *fiber.Ctx) error {
	serverStatuses, err := h.serverStatusService.GetAll()
	if err != nil {
		return response.Error(c, fiber.StatusInternalServerError, "Failed to get server statuses")
	}

	var serverStatusResponses []*dto.ServerStatusResponse
	for _, serverStatus := range serverStatuses {
		serverStatusResponses = append(serverStatusResponses, &dto.ServerStatusResponse{
			ID:   serverStatus.ID,
			Name: serverStatus.Name,
		})
	}

	return response.Success(c, fiber.StatusOK, "Server statuses retrieved successfully", serverStatusResponses)
}

func (h *ServerStatusHandler) GetByID(c *fiber.Ctx) error {
	idParam := c.Params("id")
	id, err := strconv.ParseUint(idParam, 10, 64)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid server status ID")
	}

	serverStatus, err := h.serverStatusService.GetByID(id)
	if err != nil {
		return response.Error(c, fiber.StatusNotFound, "Server status not found")
	}

	serverStatusResponse := &dto.ServerStatusResponse{
		ID:   serverStatus.ID,
		Name: serverStatus.Name,
	}

	return response.Success(c, fiber.StatusOK, "Server status retrieved successfully", serverStatusResponse)
}
