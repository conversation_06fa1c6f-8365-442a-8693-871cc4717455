package handlers

import (
	"strconv"
	"strings"

	"ops-api/internal/core/domain"
	"ops-api/internal/core/dto"
	"ops-api/internal/core/ports"
	"ops-api/pkg/utils/response"

	"github.com/gofiber/fiber/v2"
)

type NamespaceHandler struct {
	namespaceService ports.NamespaceService
}

func NewNamespaceHandler(namespaceService ports.NamespaceService) *NamespaceHandler {
	return &NamespaceHandler{
		namespaceService: namespaceService,
	}
}

func (h *NamespaceHandler) CreateNamespace(c *fiber.Ctx) error {
	var req dto.CreateNamespaceRequest
	if err := c.<PERSON>er(&req); err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid request format")
	}

	namespace, err := h.namespaceService.Create(req.Name, req.Slug, req.IsActive, req.Type, req.ClusterID)
	if err != nil {
		if strings.Contains(err.<PERSON><PERSON><PERSON>(), "access denied") {
			return response.Error(c, fiber.StatusForbidden, err.<PERSON>rror())
		}
		return response.Error(c, fiber.StatusBadRequest, err.<PERSON>r())
	}

	return response.Success(c, fiber.StatusCreated, "Namespace created successfully", dto.ToNamespaceDetailDTO(namespace))
}

func (h *NamespaceHandler) GetNamespaces(c *fiber.Ctx) error {
	userID := c.Locals("user_id").(uint64)

	filter := &ports.NamespaceFilter{}

	if name := c.Query("name"); name != "" {
		filter.Name = &name
	}

	if slug := c.Query("slug"); slug != "" {
		filter.Slug = &slug
	}

	if clusterIDStr := c.Query("cluster_id"); clusterIDStr != "" {
		if clusterID, err := strconv.ParseUint(clusterIDStr, 10, 64); err == nil {
			filter.ClusterID = &clusterID
		}
	}

	if isActiveStr := c.Query("is_active"); isActiveStr != "" {
		if isActive, err := strconv.ParseBool(isActiveStr); err == nil {
			filter.IsActive = &isActive
		}
	}

	if typeStr := c.Query("type"); typeStr != "" {
		namespaceType := domain.NamespaceType(typeStr)
		filter.Type = &namespaceType
	}

	namespaces, err := h.namespaceService.GetAllByUserWorkspaces(userID, filter)
	if err != nil {
		return response.Error(c, fiber.StatusInternalServerError, err.Error())
	}

	var responseList []*dto.NamespaceListItemResponse
	for _, namespace := range namespaces {
		responseList = append(responseList, dto.ToNamespaceListItemDTO(namespace))
	}

	return response.Success(c, fiber.StatusOK, "Namespaces retrieved successfully", responseList)
}

func (h *NamespaceHandler) GetNamespace(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid namespace ID")
	}

	namespace, err := h.namespaceService.GetByID(id)
	if err != nil {
		return response.Error(c, fiber.StatusNotFound, "Namespace not found")
	}

	return response.Success(c, fiber.StatusOK, "Namespace retrieved successfully", dto.ToNamespaceDetailDTO(namespace))
}

func (h *NamespaceHandler) UpdateNamespace(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid namespace ID")
	}

	var req dto.UpdateNamespaceRequest
	if err := c.BodyParser(&req); err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid request format")
	}

	namespace, err := h.namespaceService.Update(id, req.Name, req.Slug, req.IsActive, req.Type, req.ClusterID)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return response.Error(c, fiber.StatusNotFound, err.Error())
		}
		return response.Error(c, fiber.StatusBadRequest, err.Error())
	}

	return response.Success(c, fiber.StatusOK, "Namespace updated successfully", dto.ToNamespaceDetailDTO(namespace))
}

func (h *NamespaceHandler) DeleteNamespace(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid namespace ID")
	}

	err = h.namespaceService.Delete(id)
	if err != nil {
		return response.Error(c, fiber.StatusInternalServerError, err.Error())
	}

	return response.Success(c, fiber.StatusOK, "Namespace deleted successfully", nil)
}

func (h *NamespaceHandler) GetNamespaceTypes(c *fiber.Ctx) error {
	types := domain.GetAllNamespaceTypes()

	return response.Success(c, fiber.StatusOK, "Namespace types retrieved successfully", types)
}

func (h *NamespaceHandler) GetTemplates(c *fiber.Ctx) error {
	// Create filter for active template namespaces
	isActive := true
	templateType := domain.NamespaceTypeTemplate
	filter := &ports.NamespaceFilter{
		IsActive: &isActive,
		Type:     &templateType,
	}

	namespaces, err := h.namespaceService.GetAll(filter)
	if err != nil {
		return response.Error(c, fiber.StatusInternalServerError, "Failed to get templates")
	}

	var responseList []*dto.NamespaceListItemResponse
	for _, namespace := range namespaces {
		responseList = append(responseList, dto.ToNamespaceListItemDTO(namespace))
	}

	return response.Success(c, fiber.StatusOK, "Templates retrieved successfully", responseList)
}
