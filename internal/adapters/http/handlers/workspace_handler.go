package handlers

import (
	"strconv"
	"strings"

	"ops-api/internal/core/dto"
	"ops-api/internal/core/ports"
	"ops-api/pkg/utils/response"

	"github.com/gofiber/fiber/v2"
)

type WorkspaceHandler struct {
	workspaceService ports.WorkspaceService
}

func NewWorkspaceHandler(workspaceService ports.WorkspaceService) *WorkspaceHandler {
	return &WorkspaceHandler{
		workspaceService: workspaceService,
	}
}

// CreateWorkspace godoc
// @Summary Create a new workspace
// @Description Create a new workspace for the authenticated user
// @Tags workspaces
// @Accept json
// @Produce json
// @Param request body dto.CreateWorkspaceRequest true "Create workspace request"
// @Success 201 {object} dto.WorkspaceDetailResponse
// @Failure 400 {object} errs.ErrorResponse
// @Failure 401 {object} errs.ErrorResponse
// @Failure 500 {object} errs.ErrorResponse
// @Router /workspaces [post]
func (h *WorkspaceHandler) CreateWorkspace(c *fiber.Ctx) error {
	userID := c.Locals("user_id").(uint64)

	var req dto.CreateWorkspaceRequest
	if err := c.BodyParser(&req); err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid request format")
	}

	workspace, err := h.workspaceService.Create(userID, req.Name, req.Description)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, err.Error())
	}

	return response.Success(c, fiber.StatusCreated, "Workspace created successfully", dto.ToWorkspaceDetailDTO(workspace))
}

// GetWorkspaces godoc
// @Summary Get all workspaces
// @Description Get all workspaces with optional filtering
// @Tags workspaces
// @Accept json
// @Produce json
// @Param name query string false "Filter by workspace name"
// @Success 200 {array} dto.WorkspaceListItemResponse
// @Failure 500 {object} errs.ErrorResponse
// @Router /workspaces [get]
func (h *WorkspaceHandler) GetWorkspaces(c *fiber.Ctx) error {
	userID := c.Locals("user_id").(uint64)

	filter := &ports.WorkspaceFilter{}

	// Apply name filter if provided
	if name := c.Query("name"); name != "" {
		filter.Name = &name
	}

	// Only show workspaces owned by the authenticated user
	filter.UserID = &userID

	workspaces, err := h.workspaceService.GetAll(filter)
	if err != nil {
		return response.Error(c, fiber.StatusInternalServerError, "Failed to fetch workspaces")
	}

	var workspaceResponses []*dto.WorkspaceListItemResponse
	for _, workspace := range workspaces {
		workspaceResponses = append(workspaceResponses, dto.ToWorkspaceListItemDTO(workspace))
	}

	return response.Success(c, fiber.StatusOK, "Workspaces retrieved successfully", workspaceResponses)
}

// GetWorkspaceByID godoc
// @Summary Get workspace by ID
// @Description Get a specific workspace by its ID
// @Tags workspaces
// @Accept json
// @Produce json
// @Param id path int true "Workspace ID"
// @Success 200 {object} dto.WorkspaceDetailResponse
// @Failure 400 {object} errs.ErrorResponse
// @Failure 404 {object} errs.ErrorResponse
// @Failure 500 {object} errs.ErrorResponse
// @Router /workspaces/{id} [get]
func (h *WorkspaceHandler) GetWorkspaceByID(c *fiber.Ctx) error {
	userID := c.Locals("user_id").(uint64)

	idStr := c.Params("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid workspace ID")
	}

	workspace, err := h.workspaceService.GetByID(id)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return response.Error(c, fiber.StatusNotFound, "Workspace not found")
		}
		return response.Error(c, fiber.StatusInternalServerError, "Failed to fetch workspace")
	}

	// Check if user owns the workspace
	if workspace.UserID != userID {
		return response.Error(c, fiber.StatusForbidden, "Access denied: you can only view your own workspaces")
	}

	return response.Success(c, fiber.StatusOK, "Workspace retrieved successfully", dto.ToWorkspaceDetailDTO(workspace))
}

// UpdateWorkspace godoc
// @Summary Update workspace
// @Description Update a workspace by its ID
// @Tags workspaces
// @Accept json
// @Produce json
// @Param id path int true "Workspace ID"
// @Param request body dto.UpdateWorkspaceRequest true "Update workspace request"
// @Success 200 {object} dto.WorkspaceDetailResponse
// @Failure 400 {object} errs.ErrorResponse
// @Failure 403 {object} errs.ErrorResponse
// @Failure 404 {object} errs.ErrorResponse
// @Failure 500 {object} errs.ErrorResponse
// @Router /workspaces/{id} [put]
func (h *WorkspaceHandler) UpdateWorkspace(c *fiber.Ctx) error {
	userID := c.Locals("user_id").(uint64)

	idStr := c.Params("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid workspace ID")
	}

	var req dto.UpdateWorkspaceRequest
	if err := c.BodyParser(&req); err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid request format")
	}

	workspace, err := h.workspaceService.Update(id, userID, req.Name, req.Description)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return response.Error(c, fiber.StatusNotFound, "Workspace not found")
		}
		if strings.Contains(err.Error(), "access denied") {
			return response.Error(c, fiber.StatusForbidden, err.Error())
		}
		return response.Error(c, fiber.StatusBadRequest, err.Error())
	}

	return response.Success(c, fiber.StatusOK, "Workspace updated successfully", dto.ToWorkspaceDetailDTO(workspace))
}

// DeleteWorkspace godoc
// @Summary Delete workspace
// @Description Delete a workspace by its ID
// @Tags workspaces
// @Accept json
// @Produce json
// @Param id path int true "Workspace ID"
// @Success 200 {object} response.Response
// @Failure 400 {object} errs.ErrorResponse
// @Failure 403 {object} errs.ErrorResponse
// @Failure 404 {object} errs.ErrorResponse
// @Failure 500 {object} errs.ErrorResponse
// @Router /workspaces/{id} [delete]
func (h *WorkspaceHandler) DeleteWorkspace(c *fiber.Ctx) error {
	userID := c.Locals("user_id").(uint64)

	idStr := c.Params("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid workspace ID")
	}

	err = h.workspaceService.Delete(id, userID)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return response.Error(c, fiber.StatusNotFound, "Workspace not found")
		}
		if strings.Contains(err.Error(), "access denied") {
			return response.Error(c, fiber.StatusForbidden, err.Error())
		}
		return response.Error(c, fiber.StatusInternalServerError, "Failed to delete workspace")
	}

	return response.Success(c, fiber.StatusOK, "Workspace deleted successfully", nil)
}
