package handlers

import (
	"strconv"
	"strings"

	"ops-api/internal/core/dto"
	"ops-api/internal/core/ports"
	"ops-api/pkg/utils/response"

	"github.com/gofiber/fiber/v2"
)

type EnvironmentHandler struct {
	environmentService ports.EnvironmentService
}

func NewEnvironmentHandler(environmentService ports.EnvironmentService) *EnvironmentHandler {
	return &EnvironmentHandler{
		environmentService: environmentService,
	}
}

func (h *EnvironmentHandler) CreateEnvironment(c *fiber.Ctx) error {
	var req dto.CreateEnvironmentRequest
	if err := c.BodyParser(&req); err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid request format")
	}

	environment, err := h.environmentService.Create(req.Name, req.Value, req.DeploymentID)
	if err != nil {
		if strings.Contains(err.Error(), "access denied") {
			return response.Error(c, fiber.StatusForbidden, err.<PERSON><PERSON><PERSON>())
		}
		return response.Error(c, fiber.StatusBadRequest, err.Error())
	}

	return response.Success(c, fiber.StatusCreated, "Environment created successfully", dto.ToEnvironmentDetailDTO(environment))
}

func (h *EnvironmentHandler) GetEnvironments(c *fiber.Ctx) error {
	filter := &ports.EnvironmentFilter{}

	if name := c.Query("name"); name != "" {
		filter.Name = &name
	}

	if deploymentIDStr := c.Query("deployment_id"); deploymentIDStr != "" {
		deploymentID, err := strconv.ParseUint(deploymentIDStr, 10, 64)
		if err != nil {
			return response.Error(c, fiber.StatusBadRequest, "Invalid deployment_id parameter")
		}
		filter.DeploymentID = &deploymentID
	}

	environments, err := h.environmentService.GetAll(filter)
	if err != nil {
		return response.Error(c, fiber.StatusInternalServerError, err.Error())
	}

	var environmentDTOs []*dto.EnvironmentListItemResponse
	for _, environment := range environments {
		environmentDTOs = append(environmentDTOs, dto.ToEnvironmentListItemDTO(environment))
	}

	return response.Success(c, fiber.StatusOK, "Environments retrieved successfully", environmentDTOs)
}

func (h *EnvironmentHandler) GetEnvironmentByID(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid ID parameter")
	}

	environment, err := h.environmentService.GetByID(id)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return response.Error(c, fiber.StatusNotFound, err.Error())
		}
		return response.Error(c, fiber.StatusInternalServerError, err.Error())
	}

	return response.Success(c, fiber.StatusOK, "Environment retrieved successfully", dto.ToEnvironmentDetailDTO(environment))
}

func (h *EnvironmentHandler) UpdateEnvironment(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid ID parameter")
	}

	var req dto.UpdateEnvironmentRequest
	if err := c.BodyParser(&req); err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid request format")
	}

	environment, err := h.environmentService.Update(id, req.Name, req.Value, req.DeploymentID)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return response.Error(c, fiber.StatusNotFound, err.Error())
		}
		if strings.Contains(err.Error(), "access denied") {
			return response.Error(c, fiber.StatusForbidden, err.Error())
		}
		return response.Error(c, fiber.StatusBadRequest, err.Error())
	}

	return response.Success(c, fiber.StatusOK, "Environment updated successfully", dto.ToEnvironmentDetailDTO(environment))
}

func (h *EnvironmentHandler) DeleteEnvironment(c *fiber.Ctx) error {
	idStr := c.Params("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid ID parameter")
	}

	err = h.environmentService.Delete(id)
	if err != nil {
		if strings.Contains(err.Error(), "not found") {
			return response.Error(c, fiber.StatusNotFound, err.Error())
		}
		if strings.Contains(err.Error(), "access denied") {
			return response.Error(c, fiber.StatusForbidden, err.Error())
		}
		return response.Error(c, fiber.StatusInternalServerError, err.Error())
	}

	return response.Success(c, fiber.StatusOK, "Environment deleted successfully", nil)
}
