package handlers

import (
	"strings"

	"ops-api/internal/core/dto"
	"ops-api/internal/core/ports"
	"ops-api/pkg/utils/response"

	"github.com/gofiber/fiber/v2"
)

type OrderNamespaceHandler struct {
	orderNamespaceService ports.OrderNamespaceService
}

func NewOrderNamespaceHandler(orderNamespaceService ports.OrderNamespaceService) *OrderNamespaceHandler {
	return &OrderNamespaceHandler{
		orderNamespaceService: orderNamespaceService,
	}
}

func (h *OrderNamespaceHandler) CreateOrderNamespace(c *fiber.Ctx) error {
	var req dto.CreateOrderNamespaceRequest
	if err := c.<PERSON>(&req); err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid request format")
	}

	orderNamespace, err := h.orderNamespaceService.Create(req.OrderID, req.NamespaceID)
	if err != nil {
		if strings.Contains(err.<PERSON><PERSON><PERSON>(), "not found") {
			return response.Error(c, fiber.StatusNotFound, err.<PERSON>rror())
		}
		if strings.Contains(err.Error(), "does not have access") {
			return response.Error(c, fiber.StatusForbidden, err.Error())
		}
		if strings.Contains(err.Error(), "does not belong") {
			return response.Error(c, fiber.StatusForbidden, err.Error())
		}
		if strings.Contains(err.Error(), "required") {
			return response.Error(c, fiber.StatusBadRequest, err.Error())
		}
		return response.Error(c, fiber.StatusBadRequest, err.Error())
	}

	return response.Success(c, fiber.StatusCreated, "Order namespace created successfully", dto.ToOrderNamespaceDetailDTO(orderNamespace))
}

func (h *OrderNamespaceHandler) GetOrderNamespacesByUser(c *fiber.Ctx) error {
	// Get user ID from JWT token
	userID := h.getUserIDFromContext(c)
	if userID == 0 {
		return response.Error(c, fiber.StatusUnauthorized, "User not authenticated")
	}

	orderNamespaces, err := h.orderNamespaceService.GetAllByUser(userID)
	if err != nil {
		return response.Error(c, fiber.StatusInternalServerError, "Failed to get order namespaces")
	}

	var orderNamespaceList []dto.OrderNamespaceListItemResponse
	for _, orderNamespace := range orderNamespaces {
		orderNamespaceList = append(orderNamespaceList, *dto.ToOrderNamespaceListItemDTO(orderNamespace))
	}

	return response.Success(c, fiber.StatusOK, "Order namespaces retrieved successfully", orderNamespaceList)
}

// Helper function to get user ID from JWT context
func (h *OrderNamespaceHandler) getUserIDFromContext(c *fiber.Ctx) uint64 {
	userID := c.Locals("user_id")
	if userID == nil {
		return 0
	}

	if id, ok := userID.(uint64); ok {
		return id
	}

	return 0
}
