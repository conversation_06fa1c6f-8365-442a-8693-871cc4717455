package handlers

import (
	"ops-api/pkg/utils/response"

	"github.com/gofiber/fiber/v2"
)

type HealthHandler struct{}

func NewHealthHandler() *HealthHandler {
	return &HealthHandler{}
}

func (h *HealthHandler) Check(c *fiber.Ctx) error {
	data := fiber.Map{
		"status":  "healthy",
		"service": "ops-api",
	}
	return response.Success(c, fiber.StatusOK, "Service is healthy", data)
}

func (h *HealthHandler) Ready(c *fiber.Ctx) error {
	data := fiber.Map{
		"status": "ready",
		"checks": fiber.Map{
			"database":   "ok",
			"repository": "ok",
			"service":    "ok",
		},
	}
	return response.Success(c, fiber.StatusOK, "Service is ready", data)
}

func (h *HealthHandler) Live(c *fiber.Ctx) error {
	data := fiber.Map{
		"status": "alive",
	}
	return response.Success(c, fiber.StatusOK, "Service is alive", data)
}
