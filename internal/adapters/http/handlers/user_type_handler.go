package handlers

import (
	"strconv"

	"ops-api/internal/core/dto"
	"ops-api/internal/core/ports"
	"ops-api/pkg/utils/response"

	"github.com/gofiber/fiber/v2"
)

type UserTypeHandler struct {
	service ports.UserTypeService
}

func NewUserTypeHandler(service ports.UserTypeService) *UserTypeHandler {
	return &UserTypeHandler{service: service}
}

// Create - สร้าง user type ใหม่
func (h *UserTypeHandler) Create(c *fiber.Ctx) error {
	var req dto.CreateUserTypeRequest
	if err := c.BodyParser(&req); err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid request body")
	}

	isActive := true
	if req.IsActive != nil {
		isActive = *req.IsActive
	}

	isAdmin := false
	if req.IsAdmin != nil {
		isAdmin = *req.IsAdmin
	}

	isMember := false
	if req.IsMember != nil {
		isMember = *req.IsMember
	}

	isSale := false
	if req.IsSale != nil {
		isSale = *req.IsSale
	}

	userType, err := h.service.Create(req.Name, req.Description, isActive, isAdmin, isMember, isSale)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, err.Error())
	}

	return response.Success(c, fiber.StatusCreated, "User type created successfully", dto.ToUserTypeDetailDTO(userType))
}

// GetAll - ดึงข้อมูล user types ทั้งหมด พร้อม filtering
func (h *UserTypeHandler) GetAll(c *fiber.Ctx) error {
	filter := &ports.UserTypeFilter{}

	// Parse query parameters for filtering
	if isActiveStr := c.Query("is_active"); isActiveStr != "" {
		if isActive, err := strconv.ParseBool(isActiveStr); err == nil {
			filter.IsActive = &isActive
		}
	}

	if isAdminStr := c.Query("is_admin"); isAdminStr != "" {
		if isAdmin, err := strconv.ParseBool(isAdminStr); err == nil {
			filter.IsAdmin = &isAdmin
		}
	}

	if name := c.Query("name"); name != "" {
		filter.Name = &name
	}

	userTypes, err := h.service.GetAll(filter)
	if err != nil {
		return response.Error(c, fiber.StatusInternalServerError, err.Error())
	}

	var userTypeResponses []*dto.UserTypeListItemResponse
	for _, userType := range userTypes {
		userTypeResponses = append(userTypeResponses, dto.ToUserTypeListItemDTO(userType))
	}

	return response.Success(c, fiber.StatusOK, "User types retrieved successfully", userTypeResponses)
}

// GetByID - ดึงข้อมูล user type ตาม ID
func (h *UserTypeHandler) GetByID(c *fiber.Ctx) error {
	id, err := strconv.ParseUint(c.Params("id"), 10, 32)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid user type ID")
	}

	userType, err := h.service.GetByID(uint(id))
	if err != nil {
		return response.Error(c, fiber.StatusNotFound, err.Error())
	}

	return response.Success(c, fiber.StatusOK, "User type retrieved successfully", dto.ToUserTypeDetailDTO(userType))
}

// Update - แก้ไขข้อมูล user type
func (h *UserTypeHandler) Update(c *fiber.Ctx) error {
	id, err := strconv.ParseUint(c.Params("id"), 10, 32)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid user type ID")
	}

	var req dto.UpdateUserTypeRequest
	if err := c.BodyParser(&req); err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid request body")
	}

	isActive := true
	if req.IsActive != nil {
		isActive = *req.IsActive
	}

	isAdmin := false
	if req.IsAdmin != nil {
		isAdmin = *req.IsAdmin
	}

	isMember := false
	if req.IsMember != nil {
		isMember = *req.IsMember
	}

	isSale := false
	if req.IsSale != nil {
		isSale = *req.IsSale
	}

	userType, err := h.service.Edit(uint(id), req.Name, req.Description, isActive, isAdmin, isMember, isSale)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, err.Error())
	}

	return response.Success(c, fiber.StatusOK, "User type updated successfully", dto.ToUserTypeDetailDTO(userType))
}

// Delete - ลบ user type
func (h *UserTypeHandler) Delete(c *fiber.Ctx) error {
	id, err := strconv.ParseUint(c.Params("id"), 10, 32)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid user type ID")
	}

	err = h.service.Remove(uint(id))
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, err.Error())
	}

	return response.SuccessWithoutData(c, fiber.StatusOK, "User type deleted successfully")
}
