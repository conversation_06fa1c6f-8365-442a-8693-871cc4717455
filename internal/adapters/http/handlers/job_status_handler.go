package handlers

import (
	"strconv"

	"ops-api/internal/core/dto"
	"ops-api/internal/core/ports"
	"ops-api/pkg/utils/response"

	"github.com/gofiber/fiber/v2"
)

type JobStatusHandler struct {
	jobStatusService ports.JobStatusService
}

func NewJobStatusHandler(jobStatusService ports.JobStatusService) *JobStatusHandler {
	return &JobStatusHandler{
		jobStatusService: jobStatusService,
	}
}

func (h *JobStatusHandler) Create(c *fiber.Ctx) error {
	var req dto.CreateJobStatusRequest
	if err := c.BodyParser(&req); err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid request format")
	}

	jobStatus, err := h.jobStatusService.Create(req.Name)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, err.Error())
	}

	return response.Success(c, fiber.StatusCreated, "Job status created successfully", dto.ToJobStatusDetailDTO(jobStatus))
}

func (h *JobStatusHandler) GetAll(c *fiber.Ctx) error {
	// Parse query parameters for filtering
	filter := &ports.JobStatusFilter{}
	if name := c.Query("name"); name != "" {
		filter.Name = &name
	}

	jobStatuses, err := h.jobStatusService.GetAll(filter)
	if err != nil {
		return response.Error(c, fiber.StatusInternalServerError, "Failed to get job statuses")
	}

	var jobStatusResponses []*dto.JobStatusListItemResponse
	for _, jobStatus := range jobStatuses {
		jobStatusResponses = append(jobStatusResponses, dto.ToJobStatusListItemDTO(jobStatus))
	}

	return response.Success(c, fiber.StatusOK, "Job statuses retrieved successfully", jobStatusResponses)
}

func (h *JobStatusHandler) GetByID(c *fiber.Ctx) error {
	idParam := c.Params("id")
	id, err := strconv.ParseUint(idParam, 10, 32)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid job status ID")
	}

	jobStatus, err := h.jobStatusService.GetByID(uint(id))
	if err != nil {
		return response.Error(c, fiber.StatusNotFound, "Job status not found")
	}

	return response.Success(c, fiber.StatusOK, "Job status retrieved successfully", dto.ToJobStatusDetailDTO(jobStatus))
}

func (h *JobStatusHandler) Update(c *fiber.Ctx) error {
	idParam := c.Params("id")
	id, err := strconv.ParseUint(idParam, 10, 32)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid job status ID")
	}

	var req dto.UpdateJobStatusRequest
	if err := c.BodyParser(&req); err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid request format")
	}

	jobStatus, err := h.jobStatusService.Edit(uint(id), req.Name)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, err.Error())
	}

	return response.Success(c, fiber.StatusOK, "Job status updated successfully", dto.ToJobStatusDetailDTO(jobStatus))
}

func (h *JobStatusHandler) Delete(c *fiber.Ctx) error {
	idParam := c.Params("id")
	id, err := strconv.ParseUint(idParam, 10, 32)
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, "Invalid job status ID")
	}

	err = h.jobStatusService.Remove(uint(id))
	if err != nil {
		return response.Error(c, fiber.StatusBadRequest, err.Error())
	}

	return response.SuccessWithoutData(c, fiber.StatusOK, "Job status deleted successfully")
}
