package handlers

import (
	"strconv"

	"ops-api/internal/core/ports"
	"ops-api/pkg/utils/response"

	"github.com/gofiber/fiber/v2"
)

type CloudflareHandler struct {
	cloudflareService ports.CloudflareService
}

func NewCloudflareHandler(cloudflareService ports.CloudflareService) *CloudflareHandler {
	return &CloudflareHandler{cloudflareService: cloudflareService}
}

func (h *CloudflareHandler) GetAllZones(c *fiber.Ctx) error {
	cloudflareResponse, err := h.cloudflareService.GetAllZones()
	if err != nil {
		return response.Error(c, fiber.StatusInternalServerError, "Failed to get zones")
	}
	return response.Success(c, fiber.StatusOK, "Zones retrieved successfully", cloudflareResponse)
}

// GetZonesWithAPI handles the API-based zones retrieval with query parameters
func (h *CloudflareHandler) GetZonesWithAPI(c *fiber.Ctx) error {
	// Parse query parameters
	page := 1
	if pageStr := c.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	perPage := 20
	if perPageStr := c.Query("per_page"); perPageStr != "" {
		if pp, err := strconv.Atoi(perPageStr); err == nil && pp > 0 && pp <= 50 {
			perPage = pp
		}
	}

	name := c.Query("name")
	accountName := c.Query("account.name")

	// Call the service method
	zonesResponse, err := h.cloudflareService.GetZonesWithAPI(page, name, perPage, accountName)
	if err != nil {
		return response.Error(c, fiber.StatusInternalServerError, "Failed to get zones from API: "+err.Error())
	}

	return response.Success(c, fiber.StatusOK, "Zones retrieved successfully from API", zonesResponse)
}
