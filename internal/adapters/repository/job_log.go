package repository

import (
	"ops-api/internal/core/domain"
	"ops-api/internal/core/ports"

	"gorm.io/gorm"
)

type JobLogRepository struct {
	db *gorm.DB
}

func NewJobLogRepository(db *gorm.DB) ports.JobLogRepository {
	return &JobLogRepository{db: db}
}

func (r *JobLogRepository) Insert(jobLog *domain.JobLog) error {
	return r.db.Create(jobLog).Error
}

func (r *JobLogRepository) FindByID(id uint64) (*domain.JobLog, error) {
	var jobLog domain.JobLog
	err := r.db.Preload("Job").First(&jobLog, id).Error
	if err != nil {
		return nil, err
	}
	return &jobLog, nil
}

func (r *JobLogRepository) FindByJobID(jobID uint64) ([]*domain.JobLog, error) {
	var jobLogs []*domain.JobLog
	err := r.db.Preload("Job").Where("job_id = ?", jobID).Order("created_at DESC").Find(&jobLogs).Error
	return jobLogs, err
}

func (r *JobLogRepository) FindAll(filter *ports.JobLogFilter) ([]*domain.JobLog, error) {
	var jobLogs []*domain.JobLog
	query := r.db.Preload("Job").Order("created_at DESC")

	if filter != nil {
		if filter.JobID != nil {
			query = query.Where("job_id = ?", *filter.JobID)
		}
		if filter.Name != nil {
			query = query.Where("name LIKE ?", "%"+*filter.Name+"%")
		}
	}

	err := query.Find(&jobLogs).Error
	return jobLogs, err
}

func (r *JobLogRepository) Update(jobLog *domain.JobLog) error {
	return r.db.Save(jobLog).Error
}

func (r *JobLogRepository) Delete(id uint64) error {
	return r.db.Delete(&domain.JobLog{}, id).Error
}
