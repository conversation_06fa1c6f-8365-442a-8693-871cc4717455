package repository

import (
	"ops-api/internal/core/domain"
	"ops-api/internal/core/ports"

	"gorm.io/gorm"
)

type DeploymentRepository struct {
	db *gorm.DB
}

func NewDeploymentRepository(db *gorm.DB) ports.DeploymentRepository {
	return &DeploymentRepository{
		db: db,
	}
}

func (r *DeploymentRepository) Insert(deployment *domain.Deployment) error {
	return r.db.Create(deployment).Error
}

func (r *DeploymentRepository) FindAll(filter *ports.DeploymentFilter) ([]*domain.Deployment, error) {
	var deployments []*domain.Deployment
	query := r.db.Preload("Namespace").
		Preload("Environments").
		Preload("Status")

	if filter != nil {
		if filter.Name != nil {
			query = query.Where("name ILIKE ?", "%"+*filter.Name+"%")
		}
		if filter.NamespaceID != nil {
			query = query.Where("namespace_id = ?", *filter.NamespaceID)
		}
	}

	err := query.Find(&deployments).Error
	return deployments, err
}

func (r *DeploymentRepository) FindByID(id uint64) (*domain.Deployment, error) {
	var deployment domain.Deployment
	err := r.db.Preload("Namespace").
		Preload("Status").
		Preload("Environments").
		First(&deployment, id).Error
	if err != nil {
		return nil, err
	}
	return &deployment, nil
}

func (r *DeploymentRepository) Update(deployment *domain.Deployment) error {
	// Use raw SQL to ensure the status_id is updated correctly
	result := r.db.Exec(
		"UPDATE deployment SET name = ?, image = ?, container_port = ?, replicas = ?, namespace_id = ?, status_id = ?, updated_at = ? WHERE id = ? AND deleted_at IS NULL",
		deployment.Name,
		deployment.Image,
		deployment.ContainerPort,
		deployment.Replicas,
		deployment.NamespaceID,
		deployment.StatusID,
		deployment.UpdatedAt,
		deployment.ID,
	)

	return result.Error
}

func (r *DeploymentRepository) Delete(id uint64) error {
	return r.db.Delete(&domain.Deployment{}, id).Error
}
