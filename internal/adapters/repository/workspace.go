package repository

import (
	"ops-api/internal/core/domain"
	"ops-api/internal/core/ports"

	"gorm.io/gorm"
)

type WorkspaceRepository struct {
	db *gorm.DB
}

func NewWorkspaceRepository(db *gorm.DB) ports.WorkspaceRepository {
	return &WorkspaceRepository{db: db}
}

func (r *WorkspaceRepository) Insert(workspace *domain.Workspace) error {
	return r.db.Create(workspace).Error
}

func (r *WorkspaceRepository) FindByID(id uint64) (*domain.Workspace, error) {
	var workspace domain.Workspace
	err := r.db.Preload("User").Preload("Clusters").First(&workspace, id).Error
	if err != nil {
		return nil, err
	}
	return &workspace, nil
}

func (r *WorkspaceRepository) FindByUserID(userID uint64) ([]*domain.Workspace, error) {
	var workspaces []*domain.Workspace
	err := r.db.Preload("User").Where("user_id = ?", userID).Order("name").Find(&workspaces).Error
	if err != nil {
		return nil, err
	}
	return workspaces, nil
}

func (r *WorkspaceRepository) FindAll(filter *ports.WorkspaceFilter) ([]*domain.Workspace, error) {
	var workspaces []*domain.Workspace
	query := r.db.Preload("User").Order("name")

	if filter != nil {
		if filter.Name != nil {
			query = query.Where("name LIKE ?", "%"+*filter.Name+"%")
		}
		if filter.UserID != nil {
			query = query.Where("user_id = ?", *filter.UserID)
		}
	}

	err := query.Find(&workspaces).Error
	if err != nil {
		return nil, err
	}
	return workspaces, nil
}

func (r *WorkspaceRepository) Update(workspace *domain.Workspace) error {
	return r.db.Save(workspace).Error
}

func (r *WorkspaceRepository) Delete(id uint64) error {
	return r.db.Delete(&domain.Workspace{}, id).Error
}
