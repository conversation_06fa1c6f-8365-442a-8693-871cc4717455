package repository

import (
	"ops-api/internal/core/domain"
	"ops-api/internal/core/ports"

	"gorm.io/gorm"
)

type IngressRepository struct {
	db *gorm.DB
}

func NewIngressRepository(db *gorm.DB) ports.IngressRepository {
	return &IngressRepository{
		db: db,
	}
}

func (r *IngressRepository) Insert(ingress *domain.Ingress) error {
	return r.db.Create(ingress).Error
}

func (r *IngressRepository) FindAll(filter *ports.IngressFilter) ([]*domain.Ingress, error) {
	var ingresses []*domain.Ingress
	query := r.db.Preload("Namespace").
		Preload("Namespace.Cluster").
		Preload("Namespace.Cluster.Workspace").
		Preload("Status")

	if filter != nil {
		if filter.Name != nil {
			query = query.Where("name ILIKE ?", "%"+*filter.Name+"%")
		}
		if filter.NamespaceID != nil {
			query = query.Where("namespace_id = ?", *filter.NamespaceID)
		}
	}

	err := query.Find(&ingresses).Error
	return ingresses, err
}

func (r *IngressRepository) FindByID(id uint64) (*domain.Ingress, error) {
	var ingress domain.Ingress
	err := r.db.Preload("Namespace").
		Preload("Namespace.Cluster").
		Preload("Namespace.Cluster.Workspace").
		Preload("IngressSpecs").
		Preload("Status").
		First(&ingress, id).Error
	if err != nil {
		return nil, err
	}
	return &ingress, nil
}

func (r *IngressRepository) Update(ingress *domain.Ingress) error {
	result := r.db.Exec(
		"UPDATE ingress SET name = ?, class = ?, namespace_id = ?, status_id = ?, updated_at = ? WHERE id = ? AND deleted_at IS NULL",
		ingress.Name,
		ingress.Class,
		ingress.NamespaceID,
		ingress.StatusID,
		ingress.UpdatedAt,
		ingress.ID,
	)

	return result.Error
}

func (r *IngressRepository) Delete(id uint64) error {
	return r.db.Delete(&domain.Ingress{}, id).Error
}
