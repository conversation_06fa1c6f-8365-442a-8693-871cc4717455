package repository

import (
	"ops-api/internal/core/domain"
	"ops-api/internal/core/ports"

	"gorm.io/gorm"
)

type IngressSpecRepository struct {
	db *gorm.DB
}

func NewIngressSpecRepository(db *gorm.DB) ports.IngressSpecRepository {
	return &IngressSpecRepository{
		db: db,
	}
}

func (r *IngressSpecRepository) Insert(ingressSpec *domain.IngressSpec) error {
	return r.db.Create(ingressSpec).Error
}

func (r *IngressSpecRepository) FindAll(filter *ports.IngressSpecFilter) ([]*domain.IngressSpec, error) {
	var ingressSpecs []*domain.IngressSpec
	query := r.db.Preload("Ingress").
		Preload("Ingress.Namespace").
		Preload("Ingress.Namespace.Cluster").
		Preload("Service").
		Preload("Service.Namespace").
		Preload("Service.Namespace.Cluster")
	if filter != nil {
		if filter.Host != nil {
			query = query.Where("host ILIKE ?", "%"+*filter.Host+"%")
		}
		if filter.Path != nil {
			query = query.Where("path ILIKE ?", "%"+*filter.Path+"%")
		}
		if filter.ServiceID != nil {
			query = query.Where("service_id = ?", *filter.ServiceID)
		}
		if filter.IngressID != nil {
			query = query.Where("ingress_id = ?", *filter.IngressID)
		}
	}

	err := query.Find(&ingressSpecs).Error
	return ingressSpecs, err
}

func (r *IngressSpecRepository) FindByID(id uint64) (*domain.IngressSpec, error) {
	var ingressSpec domain.IngressSpec
	err := r.db.Preload("Ingress").
		Preload("Ingress.Namespace").
		Preload("Ingress.Namespace.Cluster").
		Preload("Service").
		Preload("Service.Namespace").
		Preload("Service.Namespace.Cluster").
		First(&ingressSpec, id).Error
	if err != nil {
		return nil, err
	}
	return &ingressSpec, nil
}

func (r *IngressSpecRepository) FindByNamespaceID(namespaceID uint64) ([]*domain.IngressSpec, error) {
	var ingressSpecs []*domain.IngressSpec
	err := r.db.Preload("Ingress").
		Preload("Ingress.Namespace").
		Preload("Ingress.Namespace.Cluster").
		Preload("Service").
		Preload("Service.Namespace").
		Preload("Service.Namespace.Cluster").
		Joins("JOIN ingress ON ingress_spec.ingress_id = ingress.id").
		Where("ingress.namespace_id = ?", namespaceID).
		Find(&ingressSpecs).Error
	return ingressSpecs, err
}

func (r *IngressSpecRepository) FindByNamespaceAndServiceName(namespaceID uint64, serviceName string) ([]*domain.IngressSpec, error) {
	var ingressSpecs []*domain.IngressSpec
	err := r.db.Preload("Ingress").
		Preload("Ingress.Namespace").
		Preload("Ingress.Namespace.Cluster").
		Preload("Service").
		Preload("Service.Namespace").
		Preload("Service.Namespace.Cluster").
		Joins("JOIN ingress ON ingress_spec.ingress_id = ingress.id").
		Joins("JOIN service ON ingress_spec.service_id = service.id").
		Where("ingress.namespace_id = ? AND service.name = ?", namespaceID, serviceName).
		Find(&ingressSpecs).Error
	return ingressSpecs, err
}

func (r *IngressSpecRepository) Update(ingressSpec *domain.IngressSpec) error {
	return r.db.Save(ingressSpec).Error
}

func (r *IngressSpecRepository) Delete(id uint64) error {
	return r.db.Delete(&domain.IngressSpec{}, id).Error
}
