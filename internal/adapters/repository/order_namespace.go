package repository

import (
	"ops-api/internal/core/domain"
	"ops-api/internal/core/ports"

	"gorm.io/gorm"
)

type OrderNamespaceRepository struct {
	db *gorm.DB
}

func NewOrderNamespaceRepository(db *gorm.DB) ports.OrderNamespaceRepository {
	return &OrderNamespaceRepository{db: db}
}

func (r *OrderNamespaceRepository) Insert(orderNamespace *domain.OrderNamespace) error {
	err := r.db.Create(orderNamespace).Error
	if err != nil {
		return err
	}

	// Reload the order namespace with its relationships
	return r.db.Preload("Order").
		Preload("Order.User").
		Preload("Order.User.UserType").
		Preload("Order.Template").
		Preload("Order.OrderDomains").
		Preload("Namespace").
		Preload("Namespace.Cluster").
		First(orderNamespace, orderNamespace.ID).Error
}

func (r *OrderNamespaceRepository) FindByID(id uint64) (*domain.OrderNamespace, error) {
	var orderNamespace domain.OrderNamespace
	err := r.db.Preload("Order").
		Preload("Order.User").
		Preload("Order.User.UserType").
		Preload("Order.Template").
		Preload("Order.OrderDomains").
		Preload("Namespace").
		Preload("Namespace.Cluster").
		First(&orderNamespace, id).Error
	if err != nil {
		return nil, err
	}
	return &orderNamespace, nil
}

func (r *OrderNamespaceRepository) FindAll(filter *ports.OrderNamespaceFilter) ([]*domain.OrderNamespace, error) {
	var orderNamespaces []*domain.OrderNamespace
	query := r.db.Preload("Order").
		Preload("Order.User").
		Preload("Order.User.UserType").
		Preload("Order.Template").
		Preload("Order.OrderDomains").
		Preload("Namespace").
		Preload("Namespace.Cluster").
		Order("created_at DESC")

	if filter != nil {
		if filter.OrderID != nil {
			query = query.Where("order_id = ?", *filter.OrderID)
		}
		if filter.NamespaceID != nil {
			query = query.Where("namespace_id = ?", *filter.NamespaceID)
		}
		if filter.UserID != nil {
			// Join with orders table to filter by user_id
			query = query.Joins("JOIN \"order\" ON order_namespace.order_id = \"order\".id").
				Where("\"order\".user_id = ?", *filter.UserID)
		}
	}

	err := query.Find(&orderNamespaces).Error
	if err != nil {
		return nil, err
	}
	return orderNamespaces, nil
}

func (r *OrderNamespaceRepository) Update(orderNamespace *domain.OrderNamespace) error {
	err := r.db.Save(orderNamespace).Error
	if err != nil {
		return err
	}

	// Reload the order namespace with its relationships
	return r.db.Preload("Order").
		Preload("Order.User").
		Preload("Order.User.UserType").
		Preload("Order.Template").
		Preload("Order.OrderDomains").
		Preload("Namespace").
		Preload("Namespace.Cluster").
		First(orderNamespace, orderNamespace.ID).Error
}

func (r *OrderNamespaceRepository) Delete(id uint64) error {
	return r.db.Delete(&domain.OrderNamespace{}, id).Error
}
