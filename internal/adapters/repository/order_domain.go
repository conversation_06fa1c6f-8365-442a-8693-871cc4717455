package repository

import (
	"ops-api/internal/core/domain"
	"ops-api/internal/core/ports"

	"gorm.io/gorm"
)

type OrderDomainRepository struct {
	db *gorm.DB
}

func NewOrderDomainRepository(db *gorm.DB) ports.OrderDomainRepository {
	return &OrderDomainRepository{db: db}
}

func (r *OrderDomainRepository) Insert(orderDomain *domain.OrderDomain) error {
	err := r.db.Create(orderDomain).Error
	if err != nil {
		return err
	}

	// Reload the order domain with its relationships
	return r.db.Preload("Order").
		Preload("Order.User").
		Preload("Order.User.UserType").
		Preload("Order.Template").
		First(orderDomain, orderDomain.ID).Error
}

func (r *OrderDomainRepository) FindByID(id uint64) (*domain.OrderDomain, error) {
	var orderDomain domain.OrderDomain
	err := r.db.Preload("Order").
		Preload("Order.User").
		Preload("Order.User.UserType").
		Preload("Order.Template").
		First(&orderDomain, id).Error
	if err != nil {
		return nil, err
	}
	return &orderDomain, nil
}

func (r *OrderDomainRepository) FindAll(filter *ports.OrderDomainFilter) ([]*domain.OrderDomain, error) {
	var orderDomains []*domain.OrderDomain
	query := r.db.Preload("Order").
		Preload("Order.User").
		Preload("Order.User.UserType").
		Preload("Order.Template").
		Order("created_at DESC")

	if filter != nil {
		if filter.Name != nil {
			query = query.Where("name LIKE ?", "%"+*filter.Name+"%")
		}
		if filter.IsAvailable != nil {
			query = query.Where("is_available = ?", *filter.IsAvailable)
		}
		if filter.OrderID != nil {
			query = query.Where("order_id = ?", *filter.OrderID)
		}
	}

	err := query.Find(&orderDomains).Error
	if err != nil {
		return nil, err
	}
	return orderDomains, nil
}

func (r *OrderDomainRepository) Update(orderDomain *domain.OrderDomain) error {
	err := r.db.Save(orderDomain).Error
	if err != nil {
		return err
	}

	// Reload the order domain with its relationships
	return r.db.Preload("Order").
		Preload("Order.User").
		Preload("Order.User.UserType").
		Preload("Order.Template").
		First(orderDomain, orderDomain.ID).Error
}

func (r *OrderDomainRepository) Delete(id uint64) error {
	return r.db.Delete(&domain.OrderDomain{}, id).Error
}
