package repository

import (
	"ops-api/internal/core/domain"
	"ops-api/internal/core/ports"

	"gorm.io/gorm"
)

type JobRepository struct {
	db *gorm.DB
}

func NewJobRepository(db *gorm.DB) ports.JobRepository {
	return &JobRepository{db: db}
}

func (r *JobRepository) Insert(job *domain.Job) error {
	return r.db.Create(job).Error
}

func (r *JobRepository) FindByID(id uint64) (*domain.Job, error) {
	var job domain.Job
	err := r.db.Preload("JobStatus").Preload("User").Preload("JobLogs").First(&job, id).Error
	if err != nil {
		return nil, err
	}
	return &job, nil
}

func (r *JobRepository) FindAll(filter *ports.JobFilter) ([]*domain.Job, error) {
	var jobs []*domain.Job
	query := r.db.Preload("JobStatus").Preload("JobLogs").Preload("User").Order("created_at DESC")

	if filter != nil {
		if filter.Name != nil {
			query = query.Where("name LIKE ?", "%"+*filter.Name+"%")
		}
		if filter.UserID != nil {
			query = query.Where("user_id = ?", *filter.UserID)
		}
		if filter.JobStatusID != nil {
			query = query.Where("job_status_id = ?", *filter.JobStatusID)
		}
		if filter.EventID != nil {
			query = query.Where("event_id = ?", *filter.EventID)
		}
		if filter.Event != nil {
			query = query.Where("event = ?", *filter.Event)
		}
		if filter.Action != nil {
			query = query.Where("action = ?", *filter.Action)
		}
	}

	err := query.Find(&jobs).Error
	return jobs, err
}

func (r *JobRepository) Update(job *domain.Job) error {
	result := r.db.Exec(
		"UPDATE job SET name = ?, description = ?, job_status_id = ?, user_id = ?, event_id = ?, event = ?, action = ?, updated_at = ? WHERE id = ? AND deleted_at IS NULL",
		job.Name,
		job.Description,
		job.JobStatusID,
		job.UserID,
		job.EventID,
		job.Event,
		job.Action,
		job.UpdatedAt,
		job.ID,
	)

	return result.Error
}

func (r *JobRepository) Delete(id uint64) error {
	return r.db.Delete(&domain.Job{}, id).Error
}
