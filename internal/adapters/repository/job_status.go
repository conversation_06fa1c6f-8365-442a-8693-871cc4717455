package repository

import (
	"ops-api/internal/core/domain"
	"ops-api/internal/core/ports"

	"gorm.io/gorm"
)

type JobStatusRepository struct {
	db *gorm.DB
}

func NewJobStatusRepository(db *gorm.DB) ports.JobStatusRepository {
	return &JobStatusRepository{db: db}
}

func (r *JobStatusRepository) Insert(jobStatus *domain.JobStatus) error {
	return r.db.Create(jobStatus).Error
}

func (r *JobStatusRepository) FindByID(id uint) (*domain.JobStatus, error) {
	var jobStatus domain.JobStatus
	err := r.db.First(&jobStatus, id).Error
	if err != nil {
		return nil, err
	}
	return &jobStatus, nil
}

func (r *JobStatusRepository) FindAll(filter *ports.JobStatusFilter) ([]*domain.JobStatus, error) {
	var jobStatuses []*domain.JobStatus
	query := r.db.Order("name")

	if filter != nil {
		if filter.Name != nil {
			query = query.Where("name = ?", *filter.Name)
		}
	}

	err := query.Find(&jobStatuses).Error
	return jobStatuses, err
}

func (r *JobStatusRepository) Update(jobStatus *domain.JobStatus) error {
	return r.db.Save(jobStatus).Error
}

func (r *JobStatusRepository) Delete(id uint) error {
	return r.db.Delete(&domain.JobStatus{}, id).Error
}
