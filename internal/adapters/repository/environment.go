package repository

import (
	"ops-api/internal/core/domain"
	"ops-api/internal/core/ports"

	"gorm.io/gorm"
)

type EnvironmentRepository struct {
	db *gorm.DB
}

func NewEnvironmentRepository(db *gorm.DB) ports.EnvironmentRepository {
	return &EnvironmentRepository{
		db: db,
	}
}

func (r *EnvironmentRepository) Insert(environment *domain.Environment) error {
	return r.db.Create(environment).Error
}

func (r *EnvironmentRepository) FindAll(filter *ports.EnvironmentFilter) ([]*domain.Environment, error) {
	var environments []*domain.Environment
	query := r.db.Preload("Deployment").Preload("Deployment.Namespace")

	if filter != nil {
		if filter.Name != nil {
			query = query.Where("name ILIKE ?", "%"+*filter.Name+"%")
		}
		if filter.DeploymentID != nil {
			query = query.Where("deployment_id = ?", *filter.DeploymentID)
		}
	}

	err := query.Find(&environments).Error
	return environments, err
}

func (r *EnvironmentRepository) FindByID(id uint64) (*domain.Environment, error) {
	var environment domain.Environment
	err := r.db.Preload("Deployment").Preload("Deployment.Namespace").First(&environment, id).Error
	if err != nil {
		return nil, err
	}
	return &environment, nil
}

func (r *EnvironmentRepository) Update(environment *domain.Environment) error {
	return r.db.Save(environment).Error
}

func (r *EnvironmentRepository) Delete(id uint64) error {
	return r.db.Delete(&domain.Environment{}, id).Error
}
