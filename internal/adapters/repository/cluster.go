package repository

import (
	"ops-api/internal/core/domain"
	"ops-api/internal/core/ports"

	"gorm.io/gorm"
)

type ClusterRepository struct {
	db *gorm.DB
}

func NewClusterRepository(db *gorm.DB) ports.ClusterRepository {
	return &ClusterRepository{db: db}
}

func (r *ClusterRepository) Insert(cluster *domain.Cluster) error {
	return r.db.Create(cluster).Error
}

func (r *ClusterRepository) FindByID(id, namespaceId uint64) (*domain.Cluster, error) {
	var cluster domain.Cluster
	query := r.db.Preload("Workspace").
		Preload("Namespaces").
		Preload("Namespaces.Deployments").
		Preload("Namespaces.Deployments.Status").
		Preload("Namespaces.Deployments.Environments").
		Preload("Namespaces.Services").
		Preload("Namespaces.Services.Status").
		Preload("Namespaces.Services.IngressSpecs").
		Preload("Namespaces.Ingress").
		Preload("Namespaces.Ingress.Status").
		Preload("Namespaces.Ingress.IngressSpecs").
		Preload("Namespaces.Ingress.IngressSpecs.Service").
		Preload("Status")

	// Add namespace filter if namespaceId is provided
	if namespaceId > 0 {
		query = query.Preload("Namespaces", "id = ?", namespaceId).
			Preload("Namespaces.Deployments", "namespace_id = ?", namespaceId).
			Preload("Namespaces.Services", "namespace_id = ?", namespaceId).
			Preload("Namespaces.Ingress", "namespace_id = ?", namespaceId)
	}

	err := query.First(&cluster, id).Error
	if err != nil {
		return nil, err
	}
	return &cluster, nil
}

func (r *ClusterRepository) FindByName(name string) (*domain.Cluster, error) {
	var cluster domain.Cluster
	err := r.db.Where("name = ?", name).First(&cluster).Error
	if err != nil {
		return nil, err
	}
	return &cluster, nil
}

func (r *ClusterRepository) FindAll(filter *ports.ClusterFilter) ([]*domain.Cluster, error) {
	var clusters []*domain.Cluster
	query := r.db.Preload("Workspace").
		Preload("Status").
		Order("name")

	if filter != nil {
		if filter.Name != nil {
			query = query.Where("name LIKE ?", "%"+*filter.Name+"%")
		}
		if filter.Region != nil {
			query = query.Where("region = ?", *filter.Region)
		}
		if filter.WorkspaceID != nil {
			query = query.Where("workspace_id = ?", *filter.WorkspaceID)
		}
	}

	err := query.Find(&clusters).Error
	if err != nil {
		return nil, err
	}
	return clusters, nil
}

func (r *ClusterRepository) Update(cluster *domain.Cluster) error {
	// Use raw SQL to ensure the status_id is updated correctly
	result := r.db.Exec(
		"UPDATE cluster SET name = ?, region = ?, pool_name = ?, size = ?, node_count = ?, load_balance_ip = ? , workspace_id = ?, status_id = ?, updated_at = ? WHERE id = ? AND deleted_at IS NULL",
		cluster.Name,
		cluster.Region,
		cluster.PoolName,
		cluster.Size,
		cluster.NodeCount,
		cluster.LoadBalanceIP,
		cluster.WorkspaceID,
		cluster.StatusID,
		cluster.UpdatedAt,
		cluster.ID,
	)

	return result.Error
}

func (r *ClusterRepository) Delete(id uint64) error {
	return r.db.Delete(&domain.Cluster{}, id).Error
}
