package repository

import (
	"ops-api/internal/core/domain"
	"ops-api/internal/core/ports"

	"gorm.io/gorm"
)

type UserTypeRepository struct {
	db *gorm.DB
}

func NewUserTypeRepository(db *gorm.DB) ports.UserTypeRepository {
	return &UserTypeRepository{db: db}
}

func (r *UserTypeRepository) Insert(userType *domain.UserType) error {
	return r.db.Create(userType).Error
}

func (r *UserTypeRepository) FindByID(id uint) (*domain.UserType, error) {
	var userType domain.UserType
	err := r.db.First(&userType, id).Error
	if err != nil {
		return nil, err
	}
	return &userType, nil
}

func (r *UserTypeRepository) FindAll(filter *ports.UserTypeFilter) ([]*domain.UserType, error) {
	var userTypes []*domain.UserType
	query := r.db.Order("name")

	if filter != nil {
		if filter.IsActive != nil {
			query = query.Where("is_active = ?", *filter.IsActive)
		}
		if filter.IsAdmin != nil {
			query = query.Where("is_admin = ?", *filter.IsAdmin)
		}
		if filter.IsMember != nil {
			query = query.Where("is_member = ?", *filter.IsMember)
		}
		if filter.IsSales != nil {
			query = query.Where("is_sales = ?", *filter.IsSales)
		}
		if filter.Name != nil {
			query = query.Where("name = ?", *filter.Name)
		}
	}

	err := query.Find(&userTypes).Error
	return userTypes, err
}

func (r *UserTypeRepository) Update(userType *domain.UserType) error {
	return r.db.Save(userType).Error
}

func (r *UserTypeRepository) Delete(id uint) error {
	return r.db.Delete(&domain.UserType{}, id).Error
}
