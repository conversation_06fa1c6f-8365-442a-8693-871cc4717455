package repository

import (
	"ops-api/internal/core/domain"
	"ops-api/internal/core/ports"

	"gorm.io/gorm"
)

type NamespaceRepository struct {
	db *gorm.DB
}

func NewNamespaceRepository(db *gorm.DB) ports.NamespaceRepository {
	return &NamespaceRepository{db: db}
}

func (r *NamespaceRepository) Insert(namespace *domain.Namespace) error {
	err := r.db.Create(namespace).Error
	if err != nil {
		return err
	}

	// Reload the namespace with its relationships
	return r.db.Preload("Cluster").First(namespace, namespace.ID).Error
}

func (r *NamespaceRepository) FindByID(id uint64) (*domain.Namespace, error) {
	var namespace domain.Namespace
	err := r.db.Preload("Cluster.Status").
		Preload("Deployments").
		Preload("Deployments.Status").
		Preload("Deployments.Environments").
		Preload("Services").
		Preload("Services.Status").
		Preload("Services.IngressSpecs").
		Preload("Ingress").
		Preload("Ingress.Status").
		Preload("Ingress.IngressSpecs.Service").
		Preload("Domains", func(db *gorm.DB) *gorm.DB {
			return db.Order("domain.index ASC")
		}).
		First(&namespace, id).Error
	if err != nil {
		return nil, err
	}
	return &namespace, nil
}

func (r *NamespaceRepository) FindAll(filter *ports.NamespaceFilter) ([]*domain.Namespace, error) {
	var namespaces []*domain.Namespace
	query := r.db.Preload("Cluster.Status").
		Preload("Deployments").
		Preload("Deployments.Status").
		Preload("Deployments.Environments").
		Preload("Services").
		Preload("Services.Status").
		Preload("Services.IngressSpecs").
		Preload("Ingress").
		Preload("Ingress.Status").
		Preload("Ingress.IngressSpecs.Service").
		Order("updated_at DESC")

	if filter != nil {
		if filter.Name != nil {
			query = query.Where("name LIKE ?", "%"+*filter.Name+"%")
		}
		if filter.Slug != nil {
			query = query.Where("slug LIKE ?", "%"+*filter.Slug+"%")
		}
		if filter.ClusterID != nil {
			query = query.Where("cluster_id = ?", *filter.ClusterID)
		}
		if filter.IsActive != nil {
			query = query.Where("is_active = ?", *filter.IsActive)
		}
		if filter.Type != nil {
			query = query.Where("type = ?", *filter.Type)
		}
	}

	err := query.Find(&namespaces).Error
	if err != nil {
		return nil, err
	}
	return namespaces, nil
}

func (r *NamespaceRepository) Update(namespace *domain.Namespace) error {
	return r.db.Save(namespace).Error
}

func (r *NamespaceRepository) Delete(id uint64) error {
	// Use a transaction to ensure all deletions succeed or fail together
	return r.db.Transaction(func(tx *gorm.DB) error {
		// Delete in the correct order to maintain referential integrity
		// 1. Delete IngressSpecs first (they reference Ingresses and Services)
		if err := tx.Where("ingress_id IN (SELECT id FROM ingress WHERE namespace_id = ?)", id).Delete(&domain.IngressSpec{}).Error; err != nil {
			return err
		}

		// 2. Delete Ingresses (they reference Namespaces)
		if err := tx.Where("namespace_id = ?", id).Delete(&domain.Ingress{}).Error; err != nil {
			return err
		}

		// 3. Delete Services (they reference Namespaces and Deployments)
		if err := tx.Where("namespace_id = ?", id).Delete(&domain.Service{}).Error; err != nil {
			return err
		}

		// 4. Delete Environments (they reference Deployments)
		if err := tx.Where("deployment_id IN (SELECT id FROM deployment WHERE namespace_id = ?)", id).Delete(&domain.Environment{}).Error; err != nil {
			return err
		}

		// 5. Delete Deployments (they reference Namespaces)
		if err := tx.Where("namespace_id = ?", id).Delete(&domain.Deployment{}).Error; err != nil {
			return err
		}

		// 6. Delete Domains (they reference Namespaces)
		if err := tx.Where("namespace_id = ?", id).Delete(&domain.Domain{}).Error; err != nil {
			return err
		}

		// 7. Delete OrderNamespaces (they reference Namespaces)
		if err := tx.Where("namespace_id = ?", id).Delete(&domain.OrderNamespace{}).Error; err != nil {
			return err
		}

		// 8. Finally, delete the Namespace itself
		if err := tx.Delete(&domain.Namespace{}, id).Error; err != nil {
			return err
		}

		return nil
	})
}

func (r *NamespaceRepository) CountByType(namespaceType domain.NamespaceType) (int64, error) {
	var count int64
	err := r.db.Model(&domain.Namespace{}).Where("type = ?", namespaceType).Count(&count).Error
	return count, err
}
