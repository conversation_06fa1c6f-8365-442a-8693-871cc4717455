package repository

import (
	"ops-api/internal/core/domain"
	"ops-api/internal/core/ports"

	"gorm.io/gorm"
)

type ServerStatusRepository struct {
	db *gorm.DB
}

func NewServerStatusRepository(db *gorm.DB) ports.ServerStatusRepository {
	return &ServerStatusRepository{db: db}
}

func (r *ServerStatusRepository) FindAll() ([]*domain.ServerStatus, error) {
	var serverStatuses []*domain.ServerStatus
	err := r.db.Find(&serverStatuses).Error
	if err != nil {
		return nil, err
	}
	return serverStatuses, nil
}

func (r *ServerStatusRepository) FindByID(id uint64) (*domain.ServerStatus, error) {
	var serverStatus domain.ServerStatus
	err := r.db.First(&serverStatus, id).Error
	if err != nil {
		return nil, err
	}
	return &serverStatus, nil
}

func (r *ServerStatusRepository) FindByName(name string) (*domain.ServerStatus, error) {
	var serverStatus domain.ServerStatus
	err := r.db.Where("name = ?", name).First(&serverStatus).Error
	if err != nil {
		return nil, err
	}
	return &serverStatus, nil
}

func (r *ServerStatusRepository) Create(serverStatus *domain.ServerStatus) (*domain.ServerStatus, error) {
	err := r.db.Create(serverStatus).Error
	if err != nil {
		return nil, err
	}
	return serverStatus, nil
}
