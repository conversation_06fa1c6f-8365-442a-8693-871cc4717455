package services

import (
	"ops-api/internal/core/domain"
	"ops-api/internal/core/dto"
	"ops-api/internal/core/ports"
)

type DashboardService struct {
	userRepo      ports.UserRepository
	namespaceRepo ports.NamespaceRepository
	userTypeRepo  ports.UserTypeRepository
}

func NewDashboardService(
	userRepo ports.UserRepository,
	namespaceRepo ports.NamespaceRepository,
	userTypeRepo ports.UserTypeRepository,
) ports.DashboardService {
	return &DashboardService{
		userRepo:      userRepo,
		namespaceRepo: namespaceRepo,
		userTypeRepo:  userTypeRepo,
	}
}

func (s *DashboardService) GetAnalytics() (*dto.DashboardAnalyticsResponse, error) {
	// Get sales user type ID by finding the user type with IsSale = true
	// Based on the requirement, we need users where userTypeId = 3
	// But we'll use the more robust approach of finding by the sale user type
	saleTypeName := domain.UserTypeSale
	saleUserTypes, err := s.userTypeRepo.FindAll(&ports.UserTypeFilter{Name: &saleTypeName})
	if err != nil {
		return nil, err
	}

	var salesAmount int64 = 0
	if len(saleUserTypes) > 0 {
		// Count users with the sale user type ID
		salesAmount, err = s.userRepo.CountByUserTypeID(saleUserTypes[0].ID)
		if err != nil {
			return nil, err
		}
	}

	// Count namespaces with type "published"
	websAmount, err := s.namespaceRepo.CountByType(domain.NamespaceTypePublished)
	if err != nil {
		return nil, err
	}

	return &dto.DashboardAnalyticsResponse{
		SalesAmount: int(salesAmount),
		WebsAmount:  int(websAmount),
	}, nil
}
