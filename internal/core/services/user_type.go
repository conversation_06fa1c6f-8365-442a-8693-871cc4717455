package services

import (
	"errors"
	"time"

	"ops-api/internal/core/domain"
	"ops-api/internal/core/ports"
)

type UserTypeService struct {
	repo ports.UserTypeRepository
}

func NewUserTypeService(repo ports.UserTypeRepository) ports.UserTypeService {
	return &UserTypeService{repo: repo}
}

func (s *UserTypeService) Create(name, description string, isActive, isAdmin, isMember, isSale bool) (*domain.UserType, error) {
	if name == "" {
		return nil, errors.New("user type name is required")
	}

	// Check if user type already exists
	filter := &ports.UserTypeFilter{Name: &name}
	existingTypes, err := s.repo.FindAll(filter)
	if err != nil {
		return nil, err
	}
	if len(existingTypes) > 0 {
		return nil, errors.New("user type with this name already exists")
	}

	userType := &domain.UserType{
		Name:        name,
		Description: description,
		IsActive:    isActive,
		IsAdmin:     isAdmin,
		IsMember:    isMember,
		IsSale:      isSale,
	}

	err = s.repo.Insert(userType)
	if err != nil {
		return nil, err
	}

	return userType, nil
}

func (s *UserTypeService) GetByID(id uint) (*domain.UserType, error) {
	if id == 0 {
		return nil, errors.New("invalid user type ID")
	}

	return s.repo.FindByID(id)
}

func (s *UserTypeService) GetByName(name string) (*domain.UserType, error) {
	if name == "" {
		return nil, errors.New("user type name is required")
	}

	filter := &ports.UserTypeFilter{Name: &name}
	userTypes, err := s.repo.FindAll(filter)
	if err != nil {
		return nil, err
	}

	if len(userTypes) == 0 {
		return nil, errors.New("user type not found")
	}

	return userTypes[0], nil
}

func (s *UserTypeService) GetAll(filter *ports.UserTypeFilter) ([]*domain.UserType, error) {
	return s.repo.FindAll(filter)
}

func (s *UserTypeService) Edit(id uint, name, description string, isActive, isAdmin, isMember, isSale bool) (*domain.UserType, error) {
	if id == 0 {
		return nil, errors.New("invalid user type ID")
	}

	if name == "" {
		return nil, errors.New("user type name is required")
	}

	// Get existing user type
	userType, err := s.repo.FindByID(id)
	if err != nil {
		return nil, err
	}

	// Check if new name conflicts with existing user type (if name changed)
	if userType.Name != name {
		filter := &ports.UserTypeFilter{Name: &name}
		existingTypes, err := s.repo.FindAll(filter)
		if err != nil {
			return nil, err
		}
		if len(existingTypes) > 0 {
			return nil, errors.New("user type with this name already exists")
		}
	}

	// Update fields
	userType.Name = name
	userType.Description = description
	userType.IsActive = isActive
	userType.IsAdmin = isAdmin
	userType.IsMember = isMember
	userType.IsSale = isSale
	userType.UpdatedAt = time.Now()

	err = s.repo.Update(userType)
	if err != nil {
		return nil, err
	}

	return userType, nil
}

func (s *UserTypeService) Remove(id uint) error {
	if id == 0 {
		return errors.New("invalid user type ID")
	}

	// Check if user type exists
	_, err := s.repo.FindByID(id)
	if err != nil {
		return err
	}

	return s.repo.Delete(id)
}
