package services

import (
	"errors"
	"fmt"
	"ops-api/internal/core/domain"
	"ops-api/internal/core/ports"
)

type JobService struct {
	jobRepo          ports.JobRepository
	jobLogRepo       ports.JobLogRepository
	jobStatusService ports.JobStatusService
}

func NewJobService(jobRepo ports.JobRepository, jobLogRepo ports.JobLogRepository, jobStatusService ports.JobStatusService) ports.JobService {
	return &JobService{
		jobRepo:          jobRepo,
		jobLogRepo:       jobLogRepo,
		jobStatusService: jobStatusService,
	}
}

func (s *JobService) CreateJob(userID uint64, name, description string, jobStatusID uint64, eventID *uint64, event domain.JobEvent, action domain.JobAction) (*domain.Job, error) {
	if name == "" {
		return nil, errors.New("job name is required")
	}
	if userID == 0 {
		return nil, errors.New("user ID is required")
	}
	if jobStatusID == 0 {
		return nil, errors.New("job status ID is required")
	}

	job := &domain.Job{
		Name:        name,
		Description: description,
		JobStatusID: jobStatusID,
		UserID:      userID,
		EventID:     eventID,
		Event:       &event,
		Action:      &action,
	}

	err := s.jobRepo.Insert(job)
	if err != nil {
		return nil, err
	}

	// Auto-create initial job log
	jobStatus, err := s.jobStatusService.GetByID(uint(jobStatusID))
	if err != nil {
		// Log error but don't fail the job creation
		// In a real application, you might want to use a proper logger here
		jobStatus = &domain.JobStatus{Name: "Unknown"}
	}
	err = s.jobLogRepo.Insert(&domain.JobLog{
		Name:        "Job Created",
		Description: fmt.Sprintf("Job '%s' has been successfully created with status '%s' and action '%s'", name, jobStatus.Name, action),
		JobID:       job.ID,
	})
	if err != nil {
		// Log error but don't fail the job creation
		// In a real application, you might want to use a proper logger here
	}

	return s.jobRepo.FindByID(job.ID)
}

func (s *JobService) GetAllJobs(filter *ports.JobFilter) ([]*domain.Job, error) {
	return s.jobRepo.FindAll(filter)
}

func (s *JobService) GetJobByID(id uint64) (*domain.Job, error) {
	if id == 0 {
		return nil, errors.New("job ID is required")
	}

	job, err := s.jobRepo.FindByID(id)
	if err != nil {
		return nil, err
	}

	return job, nil
}

func (s *JobService) GetMyJobs(userID uint64) ([]*domain.Job, error) {
	if userID == 0 {
		return nil, errors.New("user ID is required")
	}

	filter := &ports.JobFilter{
		UserID: &userID,
	}
	jobs, err := s.jobRepo.FindAll(filter)
	if err != nil {
		return nil, err
	}

	return jobs, nil
}

func (s *JobService) UpdateJob(id uint64, userID uint64, name, description string, jobStatusID uint64, eventID *uint64, event domain.JobEvent, action domain.JobAction) (*domain.Job, error) {
	if id == 0 {
		return nil, errors.New("job ID is required")
	}
	if name == "" {
		return nil, errors.New("job name is required")
	}
	if userID == 0 {
		return nil, errors.New("user ID is required")
	}
	if jobStatusID == 0 {
		return nil, errors.New("job status ID is required")
	}

	// First, check if the job exists
	existingJob, err := s.jobRepo.FindByID(id)
	if err != nil {
		return nil, errors.New("job not found")
	}

	// Check if the user has permission to update this job
	// Users can only update their own jobs (unless they are admin, but that logic would be handled in the handler)
	if existingJob.UserID != userID {
		return nil, errors.New("unauthorized to update this job")
	}

	// Update the job fields
	existingJob.Name = name
	existingJob.Description = description
	existingJob.JobStatusID = jobStatusID
	existingJob.EventID = eventID
	existingJob.Event = &event
	existingJob.Action = &action

	err = s.jobRepo.Update(existingJob)
	if err != nil {
		return nil, err
	}

	// Create job log for the update
	jobStatus, err := s.jobStatusService.GetByID(uint(jobStatusID))
	if err != nil {
		// Log error but don't fail the job update
		// In a real application, you might want to use a proper logger here
		jobStatus = &domain.JobStatus{Name: "Unknown"}
	}
	err = s.jobLogRepo.Insert(&domain.JobLog{
		Name:        "Job Updated",
		Description: fmt.Sprintf("Job '%s' has been successfully updated with status '%s' and action '%s'. Description: %s", name, jobStatus.Name, action, description),
		JobID:       id,
	})
	if err != nil {
		// Log error but don't fail the job update
		// In a real application, you might want to use a proper logger here
	}

	return s.jobRepo.FindByID(id)
}
