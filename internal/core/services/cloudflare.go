package services

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"ops-api/internal/core/dto"
	"ops-api/internal/core/ports"
	"os"
	"os/exec"
	"path/filepath"
	"strconv"
	"sync"
	"time"
)

type CloudflareService struct {
	httpClient  *http.Client
	poolService ports.PoolService
}

func NewCloudflareService(poolService ports.PoolService) *CloudflareService {
	return &CloudflareService{
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
		poolService: poolService,
	}
}

func (s *CloudflareService) GetAllZones() (*dto.CloudflareResponse, error) {
	terraformDir := "./terraform/cloudflare"
	env := os.Getenv("ENV")
	if env != "local" {
		terraformDir = "/app/terraform/cloudflare"
	}

	// Get environment variables
	apiToken := os.Getenv("CLOUDFLARE_API_TOKEN")
	accountId := os.Getenv("CLOUDFLARE_ACCOUNT_ID")

	// Prepare variable flags for Terraform commands
	varFlags := []string{
		fmt.Sprintf("-var=cloudflare_api_token=%s", apiToken),
		fmt.Sprintf("-var=cloudflare_account_id=%s", accountId),
	}

	// Initialize Terraform only if needed
	valueDB := os.Getenv("TF_BACKEND_DB")
	valueSchema := "cloudflare"
	// terraform init
	connStr := fmt.Sprintf("-backend-config=conn_str=%s", valueDB)
	schemaName := fmt.Sprintf("-backend-config=schema_name=%s", valueSchema)
	tfInit := exec.Command("terraform", "init",
		"-lock=false",
		connStr,
		schemaName,
	)
	tfInit.Dir = terraformDir
	tfInitOutput, tfInitErr := tfInit.CombinedOutput()
	if tfInitErr != nil {
		fmt.Printf("terraform init failed - output: %s\n", string(tfInitOutput))
		fmt.Printf("terraform init failed - error: %v\n", tfInitErr)
		return nil, tfInitErr
	}
	fmt.Println("terraform init successful", string(tfInitOutput))

	// Run terraform apply
	fmt.Println("Running terraform apply...")
	applyArgs := append([]string{"apply", "-auto-approve"}, varFlags...)
	tfApply := exec.Command("terraform", applyArgs...)
	tfApply.Dir = terraformDir
	tfApplyOutput, tfApplyErr := tfApply.CombinedOutput()

	if tfApplyErr != nil {
		fmt.Printf("terraform apply failed - output: %s\n", string(tfApplyOutput))
		fmt.Printf("terraform apply failed - error: %v\n", tfApplyErr)
		return nil, tfApplyErr
	}

	fmt.Println("terraform apply successful")

	// Parse terraform apply output and convert to CloudflareResponse
	return s.parseTerraformApplyOutput(string(tfApplyOutput))
}

// parseTerraformApplyOutput parses terraform apply output and converts to CloudflareResponse
func (s *CloudflareService) parseTerraformApplyOutput(output string) (*dto.CloudflareResponse, error) {
	// First, try to get JSON output using terraform output command
	if response, err := s.getTerraformOutputFromCommand(); err == nil {
		return response, nil
	}

	// If that fails, use DTO parsing methods
	return dto.NewCloudflareResponseFromTerraformOutput(output)
}

// getTerraformOutputFromCommand gets terraform output using terraform output command
func (s *CloudflareService) getTerraformOutputFromCommand() (*dto.CloudflareResponse, error) {
	terraformDir := "./terraform/cloudflare"
	env := os.Getenv("ENV")
	if env != "local" {
		terraformDir = "/app/terraform/cloudflare"
	}

	// Get terraform output in JSON format
	tfOutput := exec.Command("terraform", "output", "-json")
	tfOutput.Dir = terraformDir
	outputBytes, err := tfOutput.CombinedOutput()

	if err != nil {
		fmt.Printf("terraform output failed - output: %s\n", string(outputBytes))
		fmt.Printf("terraform output failed - error: %v\n", err)
		return nil, err
	}

	return dto.NewCloudflareResponseFromJSON(string(outputBytes))
}

// forceInitializeTerraform forces Terraform re-initialization
func (s *CloudflareService) forceInitializeTerraform(terraformDir string) error {
	fmt.Println("Force re-initializing Terraform...")

	// Remove .terraform directory to force clean init
	terraformStateDir := filepath.Join(terraformDir, ".terraform")
	if err := os.RemoveAll(terraformStateDir); err != nil {
		fmt.Printf("Warning: failed to remove .terraform directory: %v\n", err)
	}

	// Remove lock file
	lockFile := filepath.Join(terraformDir, ".terraform.lock.hcl")
	if err := os.Remove(lockFile); err != nil && !os.IsNotExist(err) {
		fmt.Printf("Warning: failed to remove lock file: %v\n", err)
	}

	// Run terraform init
	tfInit := exec.Command("terraform", "init", "-lock=false")
	tfInit.Dir = terraformDir
	tfInitOutput, tfInitErr := tfInit.CombinedOutput()

	if tfInitErr != nil {
		fmt.Printf("terraform force init failed - output: %s\n", string(tfInitOutput))
		fmt.Printf("terraform force init failed - error: %v\n", tfInitErr)
		return tfInitErr
	}

	fmt.Println("terraform force init successful")
	return nil
}

// GetZonesWithAPI makes a direct API call to Cloudflare zones endpoint
func (s *CloudflareService) GetZonesWithAPI(page int, name string, perPage int, accountName string) (*dto.CloudflareAPIZonesResponse, error) {
	// Get API token from environment
	apiToken := os.Getenv("CLOUDFLARE_API_TOKEN")
	if apiToken == "" {
		return nil, fmt.Errorf("CLOUDFLARE_API_TOKEN environment variable is not set")
	}

	// Set default values
	if page <= 0 {
		page = 1
	}
	if perPage <= 0 {
		perPage = 20
	}
	if perPage > 50 {
		perPage = 50
	}

	// Build API URL with query parameters
	apiURL := "https://api.cloudflare.com/client/v4/zones"
	params := url.Values{}
	params.Set("page", strconv.Itoa(page))
	params.Set("per_page", strconv.Itoa(perPage))

	if name != "" {
		params.Set("name", name)
	}
	if accountName != "" {
		params.Set("account.name", accountName)
	}

	fullURL := fmt.Sprintf("%s?%s", apiURL, params.Encode())

	fmt.Printf("Making Cloudflare API request to: %s\n", fullURL)

	// Create HTTP request
	req, err := http.NewRequest("GET", fullURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %v", err)
	}

	// Add headers
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", apiToken))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	req.Header.Set("User-Agent", "ops-admin-api/1.0")

	// Make the request
	resp, err := s.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to execute request: %v", err)
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %v", err)
	}

	fmt.Printf("Cloudflare API response status: %d\n", resp.StatusCode)

	// Check for HTTP errors
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	// Parse JSON response
	var apiResponse dto.CloudflareAPIZonesResponse
	if err := json.Unmarshal(body, &apiResponse); err != nil {
		fmt.Printf("Failed to parse JSON response: %v\n", err)
		fmt.Printf("Response body: %s\n", string(body))
		return nil, fmt.Errorf("failed to parse JSON response: %v", err)
	}

	// Check if API call was successful
	if !apiResponse.Success {
		errorMsg := "API call failed"
		if len(apiResponse.Errors) > 0 {
			errorMsg = fmt.Sprintf("API call failed: %s", apiResponse.Errors[0].Message)
		}
		return nil, fmt.Errorf(errorMsg)
	}

	fmt.Printf("Successfully retrieved %d zones from Cloudflare API\n", len(apiResponse.Result))

	// Fetch always_use_https setting for each zone
	err = s.enrichZonesWithHTTPSSettings(&apiResponse, apiToken)
	if err != nil {
		fmt.Printf("Warning: Failed to enrich zones with HTTPS settings: %v\n", err)
		// Don't fail the entire request, just log the warning
	}

	return &apiResponse, nil
}

// enrichZonesWithHTTPSSettings fetches always_use_https setting for each zone using the pool service
func (s *CloudflareService) enrichZonesWithHTTPSSettings(response *dto.CloudflareAPIZonesResponse, apiToken string) error {
	if len(response.Result) == 0 {
		return nil
	}

	// Use a wait group to handle task completion
	var wg sync.WaitGroup
	// Use a mutex to protect concurrent writes to the response slice
	var mu sync.Mutex

	for i := range response.Result {
		wg.Add(1)

		// Create a task for each zone
		zoneIndex := i // Capture loop variable
		task := func() {
			defer wg.Done()

			zoneID := response.Result[zoneIndex].ID
			httpsValue := s.getAlwaysUseHTTPSSetting(zoneID, apiToken)

			// Protect concurrent write to response slice
			mu.Lock()
			response.Result[zoneIndex].AlwaysUseHTTPS = httpsValue
			mu.Unlock()
		}

		// Submit task to pool service
		if err := s.poolService.Submit(task); err != nil {
			fmt.Printf("Failed to submit HTTPS setting task for zone %s to pool service: %v\n", response.Result[zoneIndex].ID, err)
			// Fallback to direct execution
			go func() {
				defer func() {
					if r := recover(); r != nil {
						fmt.Printf("Recovered from panic in enrichZonesWithHTTPSSettings fallback: %v\n", r)
					}
				}()
				task()
			}()
		}
	}

	wg.Wait()
	return nil
}

// getAlwaysUseHTTPSSetting fetches the always_use_https setting for a specific zone
func (s *CloudflareService) getAlwaysUseHTTPSSetting(zoneID, apiToken string) string {
	// Build API URL for the specific zone setting
	settingURL := fmt.Sprintf("https://api.cloudflare.com/client/v4/zones/%s/settings/always_use_https", zoneID)

	// Create HTTP request with timeout context
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	req, err := http.NewRequestWithContext(ctx, "GET", settingURL, nil)
	if err != nil {
		fmt.Printf("Failed to create request for zone %s HTTPS setting: %v\n", zoneID, err)
		return ""
	}

	// Add headers
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", apiToken))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	req.Header.Set("User-Agent", "ops-admin-api/1.0")

	// Make the request
	resp, err := s.httpClient.Do(req)
	if err != nil {
		fmt.Printf("Failed to fetch HTTPS setting for zone %s: %v\n", zoneID, err)
		return ""
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("Failed to read HTTPS setting response for zone %s: %v\n", zoneID, err)
		return ""
	}

	// Check for HTTP errors
	if resp.StatusCode != http.StatusOK {
		fmt.Printf("HTTPS setting API request failed for zone %s with status %d: %s\n", zoneID, resp.StatusCode, string(body))
		return ""
	}

	// Parse JSON response
	var settingResponse dto.CloudflareAPISettingResponse
	if err := json.Unmarshal(body, &settingResponse); err != nil {
		fmt.Printf("Failed to parse HTTPS setting JSON response for zone %s: %v\n", zoneID, err)
		return ""
	}

	// Check if API call was successful
	if !settingResponse.Success {
		errorMsg := "Unknown error"
		if len(settingResponse.Errors) > 0 {
			errorMsg = settingResponse.Errors[0].Message
		}
		fmt.Printf("HTTPS setting API call failed for zone %s: %s\n", zoneID, errorMsg)
		return ""
	}

	fmt.Printf("Successfully retrieved HTTPS setting for zone %s: %s\n", zoneID, settingResponse.Result.Value)
	return settingResponse.Result.Value
}

// EnableAlwaysUseHTTPS enables the "Always Use HTTPS" setting for a specific Cloudflare zone
func (s *CloudflareService) EnableAlwaysUseHTTPS(zoneID string) (*dto.CloudflareAPISettingResponse, error) {
	// Validate input
	if zoneID == "" {
		return nil, fmt.Errorf("zoneID cannot be empty")
	}

	// Get API token from environment
	apiToken := os.Getenv("CLOUDFLARE_API_TOKEN")
	if apiToken == "" {
		return nil, fmt.Errorf("CLOUDFLARE_API_TOKEN environment variable is not set")
	}

	// Build API URL for the specific zone setting
	settingURL := fmt.Sprintf("https://api.cloudflare.com/client/v4/zones/%s/settings/always_use_https", zoneID)

	// Prepare request body with hardcoded "on" value
	requestBody := map[string]string{
		"value": "on",
	}

	jsonBody, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request body: %v", err)
	}

	fmt.Printf("Enabling Always Use HTTPS for zone %s\n", zoneID)

	// Create HTTP request with timeout context
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	req, err := http.NewRequestWithContext(ctx, "PATCH", settingURL, bytes.NewBuffer(jsonBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create request for zone %s: %v", zoneID, err)
	}

	// Add headers
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", apiToken))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	req.Header.Set("User-Agent", "ops-admin-api/1.0")

	// Make the request
	resp, err := s.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to execute request for zone %s: %v", zoneID, err)
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body for zone %s: %v", zoneID, err)
	}

	fmt.Printf("Always Use HTTPS API response for zone %s - Status: %d\n", zoneID, resp.StatusCode)

	// Check for HTTP errors
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API request failed for zone %s with status %d: %s", zoneID, resp.StatusCode, string(body))
	}

	// Parse JSON response
	var settingResponse dto.CloudflareAPISettingResponse
	if err := json.Unmarshal(body, &settingResponse); err != nil {
		fmt.Printf("Failed to parse JSON response for zone %s: %v\n", zoneID, err)
		fmt.Printf("Response body: %s\n", string(body))
		return nil, fmt.Errorf("failed to parse JSON response for zone %s: %v", zoneID, err)
	}

	// Check if API call was successful
	if !settingResponse.Success {
		errorMsg := "Unknown error"
		if len(settingResponse.Errors) > 0 {
			errorMsg = settingResponse.Errors[0].Message
		}
		return nil, fmt.Errorf("API call failed for zone %s: %s", zoneID, errorMsg)
	}

	fmt.Printf("Successfully enabled Always Use HTTPS for zone %s: %s\n", zoneID, settingResponse.Result.Value)
	return &settingResponse, nil
}

// ListPageRules fetches all page rules for a specific zone
// Example usage:
//
//	response, err := cloudflareService.ListPageRules("zone_id_here")
//	if err != nil {
//	    log.Fatal(err)
//	}
//	for _, rule := range response.Result {
//	    fmt.Printf("Rule ID: %s, Priority: %d, Status: %s\n", rule.ID, rule.Priority, rule.Status)
//	}
func (s *CloudflareService) ListPageRules(zoneID string) (*dto.CloudflarePageRulesResponse, error) {
	// Validate input
	if zoneID == "" {
		return nil, fmt.Errorf("zoneID cannot be empty")
	}

	// Get API token from environment
	apiToken := os.Getenv("CLOUDFLARE_API_TOKEN")
	if apiToken == "" {
		return nil, fmt.Errorf("CLOUDFLARE_API_TOKEN environment variable is not set")
	}

	// Build API URL for page rules
	apiURL := fmt.Sprintf("https://api.cloudflare.com/client/v4/zones/%s/pagerules", zoneID)

	fmt.Printf("Fetching page rules for zone %s\n", zoneID)

	// Create HTTP request with timeout context
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	req, err := http.NewRequestWithContext(ctx, "GET", apiURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request for zone %s: %v", zoneID, err)
	}

	// Add headers
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", apiToken))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	req.Header.Set("User-Agent", "ops-admin-api/1.0")

	// Make the request
	resp, err := s.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to execute request for zone %s: %v", zoneID, err)
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body for zone %s: %v", zoneID, err)
	}

	fmt.Printf("Page rules API response for zone %s - Status: %d\n", zoneID, resp.StatusCode)

	// Check for HTTP errors
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API request failed for zone %s with status %d: %s", zoneID, resp.StatusCode, string(body))
	}

	// Parse JSON response
	var pageRulesResponse dto.CloudflarePageRulesResponse
	if err := json.Unmarshal(body, &pageRulesResponse); err != nil {
		fmt.Printf("Failed to parse JSON response for zone %s: %v\n", zoneID, err)
		fmt.Printf("Response body: %s\n", string(body))
		return nil, fmt.Errorf("failed to parse JSON response for zone %s: %v", zoneID, err)
	}

	// Check if API call was successful
	if !pageRulesResponse.Success {
		errorMsg := "Unknown error"
		if len(pageRulesResponse.Errors) > 0 {
			errorMsg = pageRulesResponse.Errors[0].Message
		}
		return nil, fmt.Errorf("API call failed for zone %s: %s", zoneID, errorMsg)
	}

	fmt.Printf("Successfully retrieved %d page rules for zone %s\n", len(pageRulesResponse.Result), zoneID)
	return &pageRulesResponse, nil
}

// CreatePageRule creates a new page rule for a specific zone
// Example usage:
//
//	request := &dto.CloudflarePageRuleCreateRequest{
//	    Targets: []dto.CloudflarePageRuleTarget{
//	        {
//	            Target: "url",
//	            Constraint: map[string]interface{}{
//	                "matches": "example.com/*",
//	                "operator": "matches",
//	            },
//	        },
//	    },
//	    Actions: []dto.CloudflarePageRuleAction{
//	        {
//	            ID:    "forwarding_url",
//	            Value: map[string]interface{}{
//	                "url": "https://www.example.com/$1",
//	                "status_code": 301,
//	            },
//	        },
//	    },
//	    Status: "active",
//	}
//	 response, err := cloudflareService.CreatePageRule("zone_id_here", request)
func (s *CloudflareService) CreatePageRule(zoneID string, request *dto.CloudflarePageRuleCreateRequest) (*dto.CloudflarePageRuleResponse, error) {
	// Validate input
	if zoneID == "" {
		return nil, fmt.Errorf("zoneID cannot be empty")
	}
	if request == nil {
		return nil, fmt.Errorf("request cannot be nil")
	}
	if len(request.Targets) == 0 {
		return nil, fmt.Errorf("at least one target is required")
	}
	if len(request.Actions) == 0 {
		return nil, fmt.Errorf("at least one action is required")
	}

	// Get API token from environment
	apiToken := os.Getenv("CLOUDFLARE_API_TOKEN")
	if apiToken == "" {
		return nil, fmt.Errorf("CLOUDFLARE_API_TOKEN environment variable is not set")
	}

	// Build API URL for page rules
	apiURL := fmt.Sprintf("https://api.cloudflare.com/client/v4/zones/%s/pagerules", zoneID)

	// Marshal request body
	jsonBody, err := json.Marshal(request)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request body: %v", err)
	}

	fmt.Printf("Creating page rule for zone %s\n", zoneID)

	// Create HTTP request with timeout context
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	req, err := http.NewRequestWithContext(ctx, "POST", apiURL, bytes.NewBuffer(jsonBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create request for zone %s: %v", zoneID, err)
	}

	// Add headers
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", apiToken))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	req.Header.Set("User-Agent", "ops-admin-api/1.0")

	// Make the request
	resp, err := s.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to execute request for zone %s: %v", zoneID, err)
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body for zone %s: %v", zoneID, err)
	}

	fmt.Printf("Create page rule API response for zone %s - Status: %d\n", zoneID, resp.StatusCode)

	// Check for HTTP errors
	if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusCreated {
		return nil, fmt.Errorf("API request failed for zone %s with status %d: %s", zoneID, resp.StatusCode, string(body))
	}

	// Parse JSON response
	var pageRuleResponse dto.CloudflarePageRuleResponse
	if err := json.Unmarshal(body, &pageRuleResponse); err != nil {
		fmt.Printf("Failed to parse JSON response for zone %s: %v\n", zoneID, err)
		fmt.Printf("Response body: %s\n", string(body))
		return nil, fmt.Errorf("failed to parse JSON response for zone %s: %v", zoneID, err)
	}

	// Check if API call was successful
	if !pageRuleResponse.Success {
		errorMsg := "Unknown error"
		if len(pageRuleResponse.Errors) > 0 {
			errorMsg = pageRuleResponse.Errors[0].Message
		}
		return nil, fmt.Errorf("API call failed for zone %s: %s", zoneID, errorMsg)
	}

	fmt.Printf("Successfully created page rule %s for zone %s\n", pageRuleResponse.Result.ID, zoneID)
	return &pageRuleResponse, nil
}

// DeletePageRule deletes a specific page rule from a zone
// Example usage:
//
//	response, err := cloudflareService.DeletePageRule("zone_id_here", "page_rule_id_here")
//	if err != nil {
//	    log.Fatal(err)
//	}
//	fmt.Printf("Successfully deleted page rule: %s\n", response.Result.ID)
func (s *CloudflareService) DeletePageRule(zoneID, pageRuleID string) (*dto.CloudflarePageRuleResponse, error) {
	// Validate input
	if zoneID == "" {
		return nil, fmt.Errorf("zoneID cannot be empty")
	}
	if pageRuleID == "" {
		return nil, fmt.Errorf("pageRuleID cannot be empty")
	}

	// Get API token from environment
	apiToken := os.Getenv("CLOUDFLARE_API_TOKEN")
	if apiToken == "" {
		return nil, fmt.Errorf("CLOUDFLARE_API_TOKEN environment variable is not set")
	}

	// Build API URL for specific page rule
	apiURL := fmt.Sprintf("https://api.cloudflare.com/client/v4/zones/%s/pagerules/%s", zoneID, pageRuleID)

	fmt.Printf("Deleting page rule %s for zone %s\n", pageRuleID, zoneID)

	// Create HTTP request with timeout context
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	req, err := http.NewRequestWithContext(ctx, "DELETE", apiURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request for zone %s, page rule %s: %v", zoneID, pageRuleID, err)
	}

	// Add headers
	req.Header.Set("Authorization", fmt.Sprintf("Bearer %s", apiToken))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	req.Header.Set("User-Agent", "ops-admin-api/1.0")

	// Make the request
	resp, err := s.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to execute request for zone %s, page rule %s: %v", zoneID, pageRuleID, err)
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body for zone %s, page rule %s: %v", zoneID, pageRuleID, err)
	}

	fmt.Printf("Delete page rule API response for zone %s, page rule %s - Status: %d\n", zoneID, pageRuleID, resp.StatusCode)

	// Check for HTTP errors
	if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusNoContent {
		return nil, fmt.Errorf("API request failed for zone %s, page rule %s with status %d: %s", zoneID, pageRuleID, resp.StatusCode, string(body))
	}

	// Parse JSON response (if there's content)
	var pageRuleResponse dto.CloudflarePageRuleResponse
	if len(body) > 0 {
		if err := json.Unmarshal(body, &pageRuleResponse); err != nil {
			fmt.Printf("Failed to parse JSON response for zone %s, page rule %s: %v\n", zoneID, pageRuleID, err)
			fmt.Printf("Response body: %s\n", string(body))
			return nil, fmt.Errorf("failed to parse JSON response for zone %s, page rule %s: %v", zoneID, pageRuleID, err)
		}

		// Check if API call was successful
		if !pageRuleResponse.Success {
			errorMsg := "Unknown error"
			if len(pageRuleResponse.Errors) > 0 {
				errorMsg = pageRuleResponse.Errors[0].Message
			}
			return nil, fmt.Errorf("API call failed for zone %s, page rule %s: %s", zoneID, pageRuleID, errorMsg)
		}
	} else {
		// For successful DELETE requests that return no content, create a success response
		pageRuleResponse = dto.CloudflarePageRuleResponse{
			Success: true,
			Result: dto.CloudflarePageRule{
				ID: pageRuleID,
			},
		}
	}

	fmt.Printf("Successfully deleted page rule %s for zone %s\n", pageRuleID, zoneID)
	return &pageRuleResponse, nil
}
