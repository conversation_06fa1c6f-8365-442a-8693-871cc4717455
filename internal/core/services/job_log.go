package services

import (
	"errors"
	"ops-api/internal/core/domain"
	"ops-api/internal/core/ports"
)

type JobLogService struct {
	jobLogRepo ports.JobLogRepository
	jobRepo    ports.JobRepository
}

func NewJobLogService(jobLogRepo ports.JobLogRepository, jobRepo ports.JobRepository) ports.JobLogService {
	return &JobLogService{
		jobLogRepo: jobLogRepo,
		jobRepo:    jobRepo,
	}
}

func (s *JobLogService) CreateJobLog(jobID uint64, name, description string) (*domain.JobLog, error) {
	if jobID == 0 {
		return nil, errors.New("job ID is required")
	}
	if name == "" {
		return nil, errors.New("job log name is required")
	}

	// Verify that the job exists
	_, err := s.jobRepo.FindByID(jobID)
	if err != nil {
		return nil, errors.New("job not found")
	}

	jobLog := &domain.JobLog{
		Name:        name,
		Description: description,
		JobID:       jobID,
	}

	err = s.jobLogRepo.Insert(jobLog)
	if err != nil {
		return nil, err
	}

	// Fetch the complete job log with relationships
	return s.jobLogRepo.FindByID(jobLog.ID)
}

func (s *JobLogService) GetJobLogsByJobID(jobID uint64) ([]*domain.JobLog, error) {
	if jobID == 0 {
		return nil, errors.New("job ID is required")
	}
	return s.jobLogRepo.FindByJobID(jobID)
}

func (s *JobLogService) GetJobLogByID(id uint64) (*domain.JobLog, error) {
	if id == 0 {
		return nil, errors.New("job log ID is required")
	}
	return s.jobLogRepo.FindByID(id)
}

func (s *JobLogService) AutoCreateJobLog(jobID uint64, eventName, eventDescription string) (*domain.JobLog, error) {
	if jobID == 0 {
		return nil, errors.New("job ID is required")
	}
	if eventName == "" {
		eventName = "Job Event"
	}
	if eventDescription == "" {
		eventDescription = "Automatic job log entry created due to job event"
	}

	return s.CreateJobLog(jobID, eventName, eventDescription)
}
