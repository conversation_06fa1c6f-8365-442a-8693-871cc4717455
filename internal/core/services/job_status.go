package services

import (
	"errors"

	"ops-api/internal/core/domain"
	"ops-api/internal/core/ports"
)

type JobStatusService struct {
	repo ports.JobStatusRepository
}

func NewJobStatusService(repo ports.JobStatusRepository) ports.JobStatusService {
	return &JobStatusService{repo: repo}
}

func (s *JobStatusService) Create(name string) (*domain.JobStatus, error) {
	if name == "" {
		return nil, errors.New("job status name is required")
	}

	// Check if job status already exists
	filter := &ports.JobStatusFilter{Name: &name}
	existingStatuses, err := s.repo.FindAll(filter)
	if err != nil {
		return nil, err
	}
	if len(existingStatuses) > 0 {
		return nil, errors.New("job status with this name already exists")
	}

	jobStatus := &domain.JobStatus{
		Name: name,
	}

	err = s.repo.Insert(jobStatus)
	if err != nil {
		return nil, err
	}

	return jobStatus, nil
}

func (s *JobStatusService) GetByID(id uint) (*domain.JobStatus, error) {
	if id == 0 {
		return nil, errors.New("invalid job status ID")
	}

	return s.repo.FindByID(id)
}

func (s *JobStatusService) GetByName(name string) (*domain.JobStatus, error) {
	if name == "" {
		return nil, errors.New("job status name is required")
	}

	filter := &ports.JobStatusFilter{Name: &name}
	jobStatuses, err := s.repo.FindAll(filter)
	if err != nil {
		return nil, err
	}

	if len(jobStatuses) == 0 {
		return nil, errors.New("job status not found")
	}

	return jobStatuses[0], nil
}

func (s *JobStatusService) GetAll(filter *ports.JobStatusFilter) ([]*domain.JobStatus, error) {
	return s.repo.FindAll(filter)
}

func (s *JobStatusService) Edit(id uint, name string) (*domain.JobStatus, error) {
	if id == 0 {
		return nil, errors.New("invalid job status ID")
	}

	if name == "" {
		return nil, errors.New("job status name is required")
	}

	// Get existing job status
	jobStatus, err := s.repo.FindByID(id)
	if err != nil {
		return nil, err
	}

	// Check if new name conflicts with existing job status (if name changed)
	if jobStatus.Name != name {
		filter := &ports.JobStatusFilter{Name: &name}
		existingStatuses, err := s.repo.FindAll(filter)
		if err != nil {
			return nil, err
		}
		if len(existingStatuses) > 0 {
			return nil, errors.New("job status with this name already exists")
		}
	}

	// Update fields
	jobStatus.Name = name

	err = s.repo.Update(jobStatus)
	if err != nil {
		return nil, err
	}

	return jobStatus, nil
}

func (s *JobStatusService) Remove(id uint) error {
	if id == 0 {
		return errors.New("invalid job status ID")
	}

	// Check if job status exists
	_, err := s.repo.FindByID(id)
	if err != nil {
		return err
	}

	return s.repo.Delete(id)
}
