package services

import (
	"context"
	"fmt"
	"ops-api/internal/core/ports"
	"sync"
	"time"

	"github.com/panjf2000/ants/v2"
)

// PoolService implements the shared worker pool service using ants library
type PoolService struct {
	pool      *ants.Pool
	mu        sync.RWMutex
	isRunning bool
	poolSize  int
}

// NewPoolService creates a new shared pool service with exactly 1 worker
func NewPoolService() ports.PoolService {
	return &PoolService{
		poolSize: 1, // Fixed pool size of 1 worker as requested
	}
}

// Start initializes and starts the worker pool
func (s *PoolService) Start() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if s.isRunning {
		return fmt.Errorf("pool service is already running")
	}

	// Create the pool with size 1 and proper configuration
	pool, err := ants.NewPool(s.poolSize, ants.WithOptions(ants.Options{
		// Set a reasonable expiry duration for idle workers
		ExpiryDuration: 10 * time.Second,
		// Enable preallocation for better performance
		PreAlloc: true,
		// Set maximum blocking tasks (0 means no limit)
		MaxBlockingTasks: 0,
		// Enable non-blocking mode
		Nonblocking: false,
		// Set panic handler for better error handling
		PanicHandler: func(p interface{}) {
			fmt.Printf("Worker pool panic recovered: %v\n", p)
		},
	}))

	if err != nil {
		return fmt.Errorf("failed to create worker pool: %w", err)
	}

	s.pool = pool
	s.isRunning = true

	fmt.Printf("Shared pool service started with %d worker\n", s.poolSize)
	return nil
}

// Stop gracefully shuts down the worker pool
func (s *PoolService) Stop() error {
	s.mu.Lock()
	defer s.mu.Unlock()

	if !s.isRunning {
		return fmt.Errorf("pool service is not running")
	}

	if s.pool != nil {
		// Release the pool and wait for all workers to finish
		s.pool.Release()
		s.pool = nil
	}

	s.isRunning = false
	fmt.Println("Shared pool service stopped")
	return nil
}

// Submit submits a task to the worker pool for execution
func (s *PoolService) Submit(task func()) error {
	s.mu.RLock()
	defer s.mu.RUnlock()

	if !s.isRunning || s.pool == nil {
		return fmt.Errorf("pool service is not running")
	}

	// Wrap the task with panic recovery
	wrappedTask := func() {
		defer func() {
			if r := recover(); r != nil {
				fmt.Printf("Task panic recovered in pool service: %v\n", r)
			}
		}()
		task()
	}

	return s.pool.Submit(wrappedTask)
}

// SubmitWithContext submits a task with context for cancellation support
func (s *PoolService) SubmitWithContext(ctx context.Context, task func()) error {
	s.mu.RLock()
	defer s.mu.RUnlock()

	if !s.isRunning || s.pool == nil {
		return fmt.Errorf("pool service is not running")
	}

	// Check if context is already cancelled
	select {
	case <-ctx.Done():
		return ctx.Err()
	default:
	}

	// Wrap the task with context checking and panic recovery
	wrappedTask := func() {
		defer func() {
			if r := recover(); r != nil {
				fmt.Printf("Task panic recovered in pool service: %v\n", r)
			}
		}()

		// Check context before executing
		select {
		case <-ctx.Done():
			fmt.Printf("Task cancelled due to context: %v\n", ctx.Err())
			return
		default:
			task()
		}
	}

	return s.pool.Submit(wrappedTask)
}

// IsRunning returns true if the pool is currently running
func (s *PoolService) IsRunning() bool {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.isRunning
}

// GetPoolSize returns the configured pool size (should be 1)
func (s *PoolService) GetPoolSize() int {
	return s.poolSize
}

// GetRunningWorkers returns the number of currently running workers
func (s *PoolService) GetRunningWorkers() int {
	s.mu.RLock()
	defer s.mu.RUnlock()

	if !s.isRunning || s.pool == nil {
		return 0
	}

	return s.pool.Running()
}

// GetWaitingTasks returns the number of tasks waiting in the queue
func (s *PoolService) GetWaitingTasks() int {
	s.mu.RLock()
	defer s.mu.RUnlock()

	if !s.isRunning || s.pool == nil {
		return 0
	}

	return s.pool.Waiting()
}
