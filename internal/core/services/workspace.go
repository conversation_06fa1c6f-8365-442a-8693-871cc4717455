package services

import (
	"errors"
	"ops-api/internal/core/domain"
	"ops-api/internal/core/ports"
)

type WorkspaceService struct {
	workspaceRepo ports.WorkspaceRepository
	userRepo      ports.UserRepository
}

func NewWorkspaceService(workspaceRepo ports.WorkspaceRepository, userRepo ports.UserRepository) ports.WorkspaceService {
	return &WorkspaceService{
		workspaceRepo: workspaceRepo,
		userRepo:      userRepo,
	}
}

func (s *WorkspaceService) Create(userID uint64, name, description string) (*domain.Workspace, error) {
	if name == "" {
		return nil, errors.New("name is required")
	}

	// Verify user exists
	_, err := s.userRepo.FindByID(uint(userID))
	if err != nil {
		return nil, errors.New("user not found")
	}

	workspace := &domain.Workspace{
		Name:        name,
		Description: description,
		UserID:      userID,
	}

	err = s.workspaceRepo.Insert(workspace)
	if err != nil {
		return nil, err
	}

	return workspace, nil
}

func (s *WorkspaceService) GetAll(filter *ports.WorkspaceFilter) ([]*domain.Workspace, error) {
	return s.workspaceRepo.FindAll(filter)
}

func (s *WorkspaceService) GetByID(id uint64) (*domain.Workspace, error) {
	workspace, err := s.workspaceRepo.FindByID(id)
	if err != nil {
		return nil, err
	}
	if workspace == nil {
		return nil, errors.New("workspace not found")
	}
	return workspace, nil
}

func (s *WorkspaceService) GetByUserID(userID uint64) ([]*domain.Workspace, error) {
	return s.workspaceRepo.FindByUserID(userID)
}

func (s *WorkspaceService) Update(id, userID uint64, name, description string) (*domain.Workspace, error) {
	if name == "" {
		return nil, errors.New("name is required")
	}

	workspace, err := s.workspaceRepo.FindByID(id)
	if err != nil {
		return nil, err
	}
	if workspace == nil {
		return nil, errors.New("workspace not found")
	}

	// Check if user owns the workspace
	if workspace.UserID != userID {
		return nil, errors.New("access denied: you can only update your own workspaces")
	}

	workspace.Name = name
	workspace.Description = description

	err = s.workspaceRepo.Update(workspace)
	if err != nil {
		return nil, err
	}

	return workspace, nil
}

func (s *WorkspaceService) Delete(id, userID uint64) error {
	workspace, err := s.workspaceRepo.FindByID(id)
	if err != nil {
		return err
	}
	if workspace == nil {
		return errors.New("workspace not found")
	}

	// Check if user owns the workspace
	if workspace.UserID != userID {
		return errors.New("access denied: you can only delete your own workspaces")
	}

	return s.workspaceRepo.Delete(id)
}
