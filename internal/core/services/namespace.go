package services

import (
	"errors"
	"fmt"
	"ops-api/internal/core/domain"
	"ops-api/internal/core/ports"
)

type NamespaceService struct {
	namespaceRepo      ports.NamespaceRepository
	clusterRepo        ports.ClusterRepository
	workspaceRepo      ports.WorkspaceRepository
	deploymentRepo     ports.DeploymentRepository
	serviceRepo        ports.ServiceRepository
	ingressRepo        ports.IngressRepository
	ingressSpecRepo    ports.IngressSpecRepository
	domainRepo         ports.DomainRepository
	orderDomainService ports.OrderDomainService
}

func NewNamespaceService(
	namespaceRepo ports.NamespaceRepository,
	clusterRepo ports.ClusterRepository,
	workspaceRepo ports.WorkspaceRepository,
	deploymentRepo ports.DeploymentRepository,
	serviceRepo ports.ServiceRepository,
	ingressRepo ports.IngressRepository,
	ingressSpecRepo ports.IngressSpecRepository,
	domainRepo ports.DomainRepository,
	orderDomainService ports.OrderDomainService,
) ports.NamespaceService {
	return &NamespaceService{
		namespaceRepo:      namespaceRepo,
		clusterRepo:        clusterRepo,
		workspaceRepo:      workspaceRepo,
		deploymentRepo:     deploymentRepo,
		serviceRepo:        serviceRepo,
		ingressRepo:        ingressRepo,
		ingressSpecRepo:    ingressSpecRepo,
		domainRepo:         domainRepo,
		orderDomainService: orderDomainService,
	}
}

func (s *NamespaceService) Create(name, slug string, isActive bool, Type domain.NamespaceType, clusterID uint64) (*domain.Namespace, error) {
	if name == "" {
		return nil, errors.New("name is required")
	}
	if slug == "" {
		return nil, errors.New("slug is required")
	}
	if clusterID == 0 {
		return nil, errors.New("cluster ID is required")
	}
	if Type == "" {
		return nil, errors.New("namespace type is required")
	}

	// Verify cluster exists
	_, err := s.clusterRepo.FindByID(clusterID, 0)
	if err != nil {
		return nil, errors.New("cluster not found")
	}

	namespace := &domain.Namespace{
		Name:      name,
		Slug:      slug,
		IsActive:  isActive,
		Type:      Type,
		ClusterID: clusterID,
	}

	err = s.namespaceRepo.Insert(namespace)
	if err != nil {
		return nil, err
	}

	return namespace, nil
}

func (s *NamespaceService) GetAll(filter *ports.NamespaceFilter) ([]*domain.Namespace, error) {
	return s.namespaceRepo.FindAll(filter)
}

func (s *NamespaceService) GetAllByUserWorkspaces(userID uint64, filter *ports.NamespaceFilter) ([]*domain.Namespace, error) {
	// Get user's workspaces first
	workspaceFilter := &ports.WorkspaceFilter{
		UserID: &userID,
	}
	userWorkspaces, err := s.workspaceRepo.FindAll(workspaceFilter)
	if err != nil {
		return nil, err
	}

	// If user has no workspaces, return empty result
	if len(userWorkspaces) == 0 {
		return []*domain.Namespace{}, nil
	}

	// Create a map of user's workspace IDs for efficient lookup
	userWorkspaceIDs := make(map[uint64]bool)
	for _, workspace := range userWorkspaces {
		userWorkspaceIDs[workspace.ID] = true
	}

	// Get all clusters from user's workspaces
	clusterFilter := &ports.ClusterFilter{}
	allClusters, err := s.clusterRepo.FindAll(clusterFilter)
	if err != nil {
		return nil, err
	}

	// Create a map of cluster IDs that belong to user's workspaces
	userClusterIDs := make(map[uint64]bool)
	for _, cluster := range allClusters {
		if userWorkspaceIDs[cluster.WorkspaceID] {
			userClusterIDs[cluster.ID] = true
		}
	}

	// Get all namespaces and filter by user's clusters
	allNamespaces, err := s.namespaceRepo.FindAll(filter)
	if err != nil {
		return nil, err
	}

	// Filter namespaces to only include those in user's clusters
	var userNamespaces []*domain.Namespace
	for _, namespace := range allNamespaces {
		if userClusterIDs[namespace.ClusterID] {
			userNamespaces = append(userNamespaces, namespace)
		}
	}

	return userNamespaces, nil
}

func (s *NamespaceService) GetByID(id uint64) (*domain.Namespace, error) {
	return s.namespaceRepo.FindByID(id)
}

func (s *NamespaceService) Update(id uint64, name, slug string, isActive bool, Type domain.NamespaceType, clusterID uint64) (*domain.Namespace, error) {
	if name == "" {
		return nil, errors.New("name is required")
	}
	if slug == "" {
		return nil, errors.New("slug is required")
	}
	if clusterID == 0 {
		return nil, errors.New("cluster ID is required")
	}

	// Verify cluster exists
	_, err := s.clusterRepo.FindByID(clusterID, 0)
	if err != nil {
		return nil, errors.New("cluster not found")
	}

	// Find existing namespace
	namespace, err := s.namespaceRepo.FindByID(id)
	if err != nil {
		return nil, err
	}

	// Update fields
	namespace.Name = name
	namespace.Slug = slug
	namespace.IsActive = isActive
	namespace.Type = Type
	namespace.ClusterID = clusterID

	err = s.namespaceRepo.Update(namespace)
	if err != nil {
		return nil, err
	}

	return namespace, nil
}

func (s *NamespaceService) Delete(id uint64) error {
	if id == 0 {
		return errors.New("id is required")
	}

	// First, verify the namespace exists
	_, err := s.namespaceRepo.FindByID(id)
	if err != nil {
		return errors.New("namespace not found")
	}

	// Store domain information for updating order domain availability after deletion
	var domainInfos []struct {
		namespaceID uint64
		name        string
	}

	// Get all domains for this namespace to update order domain availability later
	domainFilter := &ports.DomainFilter{
		NamespaceID: &id,
	}
	domains, err := s.domainRepo.FindAll(domainFilter)
	if err != nil {
		// Log error but continue with deletion
		fmt.Printf("Warning: Could not retrieve domains for namespace %d: %v\n", id, err)
	} else {
		for _, domain := range domains {
			domainInfos = append(domainInfos, struct {
				namespaceID uint64
				name        string
			}{
				namespaceID: domain.NamespaceID,
				name:        domain.Name,
			})
		}
	}

	// Perform cascading deletion
	err = s.namespaceRepo.Delete(id)
	if err != nil {
		return err
	}

	// After successful deletion, update related order domains availability to false
	// This runs asynchronously to avoid blocking the deletion response
	go s.updateRelatedOrderDomainsAvailabilityOnNamespaceDeletion(domainInfos)

	return nil
}

// updateRelatedOrderDomainsAvailabilityOnNamespaceDeletion updates the availability status of related order domains
// to false when a namespace (and its domains) are deleted. This runs asynchronously.
func (s *NamespaceService) updateRelatedOrderDomainsAvailabilityOnNamespaceDeletion(domainInfos []struct {
	namespaceID uint64
	name        string
}) {
	for _, domainInfo := range domainInfos {
		// Get related order domains using the namespace ID and domain name
		relatedOrderDomains, err := s.orderDomainService.GetByNamespaceDomain(domainInfo.namespaceID, domainInfo.name)
		if err != nil {
			// Log error but continue processing other domains
			fmt.Printf("Error retrieving related order domains for namespace %d and domain %s during namespace deletion: %v\n", domainInfo.namespaceID, domainInfo.name, err)
			continue
		}

		// Update availability status to false for each related order domain
		for _, orderDomain := range relatedOrderDomains {
			_, err := s.orderDomainService.UpdateAvailability(orderDomain.ID, false)
			if err != nil {
				// Log error but continue processing other order domains
				fmt.Printf("Error updating availability to false for order domain %d during namespace deletion: %v\n", orderDomain.ID, err)
				continue
			}
			fmt.Printf("Successfully updated availability to false for order domain %d (name: %s) after namespace deletion\n", orderDomain.ID, orderDomain.Name)
		}

		if len(relatedOrderDomains) > 0 {
			fmt.Printf("Completed automated workflow on namespace deletion: updated %d related order domains to unavailable for namespace %d and domain %s\n", len(relatedOrderDomains), domainInfo.namespaceID, domainInfo.name)
		}
	}
}
