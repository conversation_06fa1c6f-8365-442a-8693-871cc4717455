package services

import (
	"ops-api/internal/core/domain"
	"ops-api/internal/core/ports"
)

type ServerStatusService struct {
	serverStatusRepo ports.ServerStatusRepository
}

func NewServerStatusService(serverStatusRepo ports.ServerStatusRepository) ports.ServerStatusService {
	return &ServerStatusService{
		serverStatusRepo: serverStatusRepo,
	}
}

func (s *ServerStatusService) GetAll() ([]*domain.ServerStatus, error) {
	return s.serverStatusRepo.FindAll()
}

func (s *ServerStatusService) GetByID(id uint64) (*domain.ServerStatus, error) {
	return s.serverStatusRepo.FindByID(id)
}

func (s *ServerStatusService) GetByName(name string) (*domain.ServerStatus, error) {
	return s.serverStatusRepo.FindByName(name)
}

func (s *ServerStatusService) Create(name string) (*domain.ServerStatus, error) {
	serverStatus := &domain.ServerStatus{
		Name: domain.ServerStatusType(name),
	}
	return s.serverStatusRepo.Create(serverStatus)
}
