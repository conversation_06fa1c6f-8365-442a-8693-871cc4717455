package services

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"strings"
	"time"

	"ops-api/internal/core/dto"
	"ops-api/internal/core/ports"
)

type NamecheapService struct {
	httpClient *http.Client
}

func NewNamecheapService() ports.NamecheapService {
	return &NamecheapService{
		httpClient: &http.Client{
			Timeout: 30 * time.Second,
		},
	}
}

func (s *NamecheapService) CheckDomains(domains []string) (*dto.NamecheapCheckResponse, error) {
	if len(domains) == 0 {
		return nil, errors.New("domains list cannot be empty")
	}

	// Validate required environment variables
	proxyEndpoint := os.Getenv("NAMECHEAP_PROXY_ENDPOINT")
	apiUser := os.Getenv("NAMECHEAP_API_USER")
	apiKey := os.Getenv("NAMECHEAP_API_KEY")
	clientIP := os.Getenv("NAMECHEAP_CLIENT_IP")

	if proxyEndpoint == "" {
		return nil, errors.New("NAMECHEAP_PROXY_ENDPOINT environment variable is required")
	}
	if apiUser == "" {
		return nil, errors.New("NAMECHEAP_API_USER environment variable is required")
	}
	if apiKey == "" {
		return nil, errors.New("NAMECHEAP_API_KEY environment variable is required")
	}
	if clientIP == "" {
		return nil, errors.New("NAMECHEAP_CLIENT_IP environment variable is required")
	}

	// Validate domains
	for _, domain := range domains {
		if strings.TrimSpace(domain) == "" {
			return nil, errors.New("domain names cannot be empty")
		}
	}

	// Convert domains array to comma-separated string
	domainList := strings.Join(domains, ",")

	// Build the API URL
	apiURL, err := s.buildCheckAPIURL(proxyEndpoint, apiUser, apiKey, clientIP, "namecheap.domains.check", domainList)
	if err != nil {
		return nil, fmt.Errorf("failed to build API URL: %v", err)
	}

	// Make the API request
	proxyResponse, err := s.makeAPIRequest(apiURL)
	if err != nil {
		return nil, fmt.Errorf("failed to make API request: %v", err)
	}

	// Convert the response to the expected format
	return dto.ConvertNamecheapProxyResponse(proxyResponse), nil
}

// extractTLD extracts the top-level domain from a domain name
// For example: "example.com" -> "com", "test.co.uk" -> "co.uk"
func (s *NamecheapService) extractTLD(domain string) string {
	parts := strings.Split(domain, ".")
	if len(parts) < 2 {
		return ""
	}

	// Handle common two-part TLDs like co.uk, com.au, etc.
	if len(parts) >= 3 {
		secondLevelDomains := map[string]bool{
			"co": true, "com": true, "net": true, "org": true, "gov": true, "edu": true,
			"ac": true, "mil": true, "int": true, "biz": true, "info": true,
		}

		lastPart := parts[len(parts)-1]
		secondLastPart := parts[len(parts)-2]

		// Check if it's a known two-part TLD
		if secondLevelDomains[secondLastPart] {
			return secondLastPart + "." + lastPart
		}
	}

	// Return the last part (standard TLD)
	return parts[len(parts)-1]
}

// CheckDomainsWithPricing checks domain availability and includes pricing information
func (s *NamecheapService) CheckDomainsWithPricing(domains []string) (*dto.NamecheapCheckResponseWithPricing, error) {
	if len(domains) == 0 {
		return nil, errors.New("domains list cannot be empty")
	}

	// First, get the basic domain check results
	checkResponse, err := s.CheckDomains(domains)
	if err != nil {
		return nil, fmt.Errorf("failed to check domains: %v", err)
	}

	// Extract unique TLDs from the domains
	tldSet := make(map[string]bool)
	for _, domain := range domains {
		tld := s.extractTLD(domain)
		if tld != "" {
			tldSet[tld] = true
		}
	}

	// Get pricing information for each unique TLD
	pricingMap := make(map[string][]dto.NamecheapPrice)
	for tld := range tldSet {
		pricingResponse, err := s.GetPricing(tld)
		if err != nil {
			// Log the error but continue with other TLDs
			fmt.Printf("Warning: Failed to get pricing for TLD %s: %v\n", tld, err)
			continue
		}

		// Extract the Price array from the pricing response
		if pricingResponse != nil &&
			len(pricingResponse.CommandResponse.UserGetPricingResult.ProductType.ProductCategory.Product.Price) > 0 {
			pricingMap[tld] = pricingResponse.CommandResponse.UserGetPricingResult.ProductType.ProductCategory.Product.Price
		}
	}

	// Create enhanced response with pricing information
	enhancedResults := make([]dto.NamecheapDomainCheckResultWithPricing, 0, len(checkResponse.CommandResponse.DomainCheckResult))

	for _, result := range checkResponse.CommandResponse.DomainCheckResult {
		enhancedResult := dto.NamecheapDomainCheckResultWithPricing{
			Domain:                   result.Domain,
			Available:                result.Available,
			ErrorNo:                  result.ErrorNo,
			Description:              result.Description,
			IsPremiumName:            result.IsPremiumName,
			PremiumRegistrationPrice: result.PremiumRegistrationPrice,
			PremiumRenewalPrice:      result.PremiumRenewalPrice,
			PremiumRestorePrice:      result.PremiumRestorePrice,
			PremiumTransferPrice:     result.PremiumTransferPrice,
			IcannFee:                 result.IcannFee,
			EapFee:                   result.EapFee,
		}

		// Add pricing information if available
		tld := s.extractTLD(result.Domain)
		if prices, exists := pricingMap[tld]; exists {
			// Filter prices to only include Duration = "1"
			var filteredPrices []dto.NamecheapPrice
			for _, price := range prices {
				if price.Duration == "1" {
					filteredPrices = append(filteredPrices, price)
				}
			}
			enhancedResult.Price = filteredPrices
		}

		enhancedResults = append(enhancedResults, enhancedResult)
	}

	return &dto.NamecheapCheckResponseWithPricing{
		DomainCheckResult: enhancedResults,
	}, nil
}

func (s *NamecheapService) buildCheckAPIURL(proxyEndpoint, apiUser, apiKey, clientIP, command, domainList string) (string, error) {
	baseURL, err := url.Parse(proxyEndpoint)
	if err != nil {
		return "", fmt.Errorf("invalid proxy endpoint URL: %v", err)
	}

	// Add query parameters
	params := url.Values{}
	params.Add("ApiUser", apiUser)
	params.Add("ApiKey", apiKey)
	params.Add("UserName", apiUser)
	params.Add("Command", command)
	params.Add("ClientIp", clientIP)
	params.Add("DomainList", domainList)

	baseURL.RawQuery = params.Encode()
	return baseURL.String(), nil
}

func (s *NamecheapService) makeAPIRequest(apiURL string) (*dto.NamecheapProxyResponse, error) {
	// Create GET request
	req, err := http.NewRequest("GET", apiURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %v", err)
	}

	// Add headers
	req.Header.Set("Accept", "application/json")
	req.Header.Set("User-Agent", "ops-admin-api/1.0")

	// Make the request
	resp, err := s.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to execute request: %v", err)
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %v", err)
	}

	// Check HTTP status code
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	// Parse JSON response
	var proxyResponse dto.NamecheapProxyResponse
	if err := json.Unmarshal(body, &proxyResponse); err != nil {
		return nil, fmt.Errorf("failed to parse JSON response: %v", err)
	}

	// Validate response structure
	if !proxyResponse.Success {
		return nil, errors.New("API request was not successful")
	}

	if proxyResponse.Data.ApiResponse.Status != "OK" {
		errorMsg := fmt.Sprintf("Namecheap API error - Status: %s", proxyResponse.Data.ApiResponse.Status)
		if proxyResponse.Data.ApiResponse.Errors != "" {
			errorMsg = fmt.Sprintf("%s, Errors: %s", errorMsg, proxyResponse.Data.ApiResponse.Errors)
		}
		if proxyResponse.Data.ApiResponse.Warnings != "" {
			errorMsg = fmt.Sprintf("%s, Warnings: %s", errorMsg, proxyResponse.Data.ApiResponse.Warnings)
		}
		return nil, errors.New(errorMsg)
	}

	return &proxyResponse, nil
}

func (s *NamecheapService) GetPricing(productName string) (*dto.NamecheapPricingResponse, error) {
	if productName == "" {
		return nil, errors.New("product name cannot be empty")
	}

	// Validate required environment variables
	proxyEndpoint := os.Getenv("NAMECHEAP_PROXY_ENDPOINT")
	apiUser := os.Getenv("NAMECHEAP_API_USER")
	apiKey := os.Getenv("NAMECHEAP_API_KEY")
	clientIP := os.Getenv("NAMECHEAP_CLIENT_IP")

	if proxyEndpoint == "" {
		return nil, errors.New("NAMECHEAP_PROXY_ENDPOINT environment variable is required")
	}
	if apiUser == "" {
		return nil, errors.New("NAMECHEAP_API_USER environment variable is required")
	}
	if apiKey == "" {
		return nil, errors.New("NAMECHEAP_API_KEY environment variable is required")
	}
	if clientIP == "" {
		return nil, errors.New("NAMECHEAP_CLIENT_IP environment variable is required")
	}

	// Build the API URL
	apiURL, err := s.buildGetPricingAPIURL(proxyEndpoint, apiUser, apiKey, clientIP, "namecheap.users.getPricing", productName)
	if err != nil {
		return nil, fmt.Errorf("failed to build API URL: %v", err)
	}

	// Make the API request using the pricing-specific method
	pricingResponse, err := s.makeAPIRequestForPricing(apiURL)
	if err != nil {
		return nil, fmt.Errorf("failed to make API request: %v", err)
	}

	return pricingResponse, nil
}

func (s *NamecheapService) buildGetPricingAPIURL(proxyEndpoint, apiUser, apiKey, clientIP, command, productName string) (string, error) {
	baseURL, err := url.Parse(proxyEndpoint)
	if err != nil {
		return "", fmt.Errorf("invalid proxy endpoint URL: %v", err)
	}

	// Add query parameters
	params := url.Values{}
	params.Add("ApiUser", apiUser)
	params.Add("ApiKey", apiKey)
	params.Add("UserName", apiUser)
	params.Add("Command", command)
	params.Add("ClientIp", clientIP)
	params.Add("ProductType", "DOMAIN")
	params.Add("ActionName", "REGISTER")
	params.Add("ProductCategory", "DOMAINS")
	params.Add("ProductName", productName)

	baseURL.RawQuery = params.Encode()
	return baseURL.String(), nil
}

func (s *NamecheapService) makeAPIRequestForPricing(apiURL string) (*dto.NamecheapPricingResponse, error) {
	// Create GET request
	req, err := http.NewRequest("GET", apiURL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %v", err)
	}

	// Add headers
	req.Header.Set("Accept", "application/json")
	req.Header.Set("User-Agent", "ops-admin-api/1.0")

	// Make the request
	resp, err := s.httpClient.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to execute request: %v", err)
	}
	defer resp.Body.Close()

	// Read response body
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %v", err)
	}

	// Check HTTP status code
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(body))
	}

	// Parse JSON response
	var proxyResponse dto.NamecheapProxyResponse
	if err := json.Unmarshal(body, &proxyResponse); err != nil {
		return nil, fmt.Errorf("failed to parse JSON response: %v", err)
	}

	// Validate response structure
	if !proxyResponse.Success {
		return nil, errors.New("API request was not successful")
	}

	if proxyResponse.Data.ApiResponse.Status != "OK" {
		errorMsg := fmt.Sprintf("Namecheap API error - Status: %s", proxyResponse.Data.ApiResponse.Status)
		if proxyResponse.Data.ApiResponse.Errors != "" {
			errorMsg = fmt.Sprintf("%s, Errors: %s", errorMsg, proxyResponse.Data.ApiResponse.Errors)
		}
		if proxyResponse.Data.ApiResponse.Warnings != "" {
			errorMsg = fmt.Sprintf("%s, Warnings: %s", errorMsg, proxyResponse.Data.ApiResponse.Warnings)
		}
		return nil, errors.New(errorMsg)
	}

	// Convert the response to the expected format using the pricing-specific converter
	return dto.ConvertNamecheapPricingProxyResponse(&proxyResponse), nil
}
