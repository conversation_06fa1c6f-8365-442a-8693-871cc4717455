package domain

type JobEvent string
type JobAction string

const (
	eventCluster    JobEvent = "cluster"
	eventNamespace  JobEvent = "namespace"
	eventDeployment JobEvent = "deployment"
	eventService    JobEvent = "service"
	eventIngress    JobEvent = "ingress"
)

const (
	actionCreate JobAction = "create"
	actionPlan   JobAction = "plan"
	actionUpdate JobAction = "update"
	actionDelete JobAction = "delete"
)

func GetAllJobEvent() []JobEvent {
	return []JobEvent{
		eventCluster,
		eventNamespace,
		eventDeployment,
		eventService,
		eventIngress,
	}
}

func GetAllJobAction() []JobAction {
	return []JobAction{
		actionCreate,
		actionPlan,
		actionUpdate,
		actionDelete,
	}
}

type Job struct {
	BaseModel
	Name        string     `json:"name"`
	Description string     `json:"description"`
	JobStatusID uint64     `json:"job_status_id" gorm:"not null"`
	UserID      uint64     `json:"user_id" gorm:"not null"`
	EventID     *uint64    `json:"event_id,omitempty"`
	Event       *JobEvent  `json:"event,omitempty"`
	Action      *JobAction `json:"action,omitempty"`

	// Relationships
	JobStatus *JobStatus `json:"job_status,omitempty" gorm:"foreignKey:JobStatusID"`
	User      *User      `json:"user,omitempty" gorm:"foreignKey:UserID"`
	JobLogs   []*JobLog  `json:"job_logs,omitempty" gorm:"foreignKey:JobID"`
}
