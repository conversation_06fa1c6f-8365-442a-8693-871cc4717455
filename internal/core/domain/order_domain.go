package domain

type OrderDomain struct {
	BaseModel
	Name        string  `json:"name" gorm:"not null"`
	IsAvailable bool    `json:"is_available" gorm:"not null default:false"`
	Price       float64 `json:"price" gorm:"type:decimal(10,2);not null default:0"`
	OrderID     uint64  `json:"order_id" gorm:"not null"`
	Nameserver1 *string `json:"nameserver_1,omitempty" gorm:"type:varchar(255)"`
	Nameserver2 *string `json:"nameserver_2,omitempty" gorm:"type:varchar(255)"`
	IsInternal  *bool   `json:"is_internal,omitempty" gorm:"default:false"`
	IsExternal  *bool   `json:"is_external,omitempty" gorm:"default:false"`

	// Relationships
	Order *Order `json:"order,omitempty" gorm:"foreignKey:OrderID"`
}
