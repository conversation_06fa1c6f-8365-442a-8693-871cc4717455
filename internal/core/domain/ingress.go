package domain

type Ingress struct {
	BaseModel
	Name        string `json:"name" gorm:"not null"`
	Class       string `json:"class" gorm:"default:nginx"` // e.g., nginx, traefik
	NamespaceID uint64 `json:"namespace_id" gorm:"not null"`
	StatusID    uint64 `json:"status_id" gorm:"not null"` // Optional: if you want to track the status of the ingress

	// Relationships
	Namespace    *Namespace     `json:"namespace,omitempty" gorm:"foreignKey:NamespaceID"`
	Status       *ServerStatus  `json:"status,omitempty" gorm:"foreignKey:StatusID"`
	IngressSpecs []*IngressSpec `json:"ingress_specs,omitempty" gorm:"foreignKey:IngressID"`
}
