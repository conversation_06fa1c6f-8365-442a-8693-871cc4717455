package domain

type NamespaceType string

const (
	NamespaceTypeTemplate  NamespaceType = "template"
	NamespaceTypeDraft     NamespaceType = "draft"
	NamespaceTypePublished NamespaceType = "published"
)

func GetAllNamespaceTypes() []NamespaceType {
	return []NamespaceType{
		NamespaceTypeTemplate,
		NamespaceTypeDraft,
		NamespaceTypePublished,
	}
}

type Namespace struct {
	BaseModel
	Name      string        `json:"name" gorm:"not null"`
	Slug      string        `json:"slug" gorm:"not null"`
	IsActive  bool          `json:"is_active"`
	Type      NamespaceType `json:"type" gorm:"not null default:'draft'"`
	ClusterID uint64        `json:"cluster_id" gorm:"not null"`

	// Relationships
	Cluster     *Cluster      `json:"cluster,omitempty" gorm:"foreignKey:ClusterID"`
	Deployments []*Deployment `json:"deployments,omitempty" gorm:"foreignKey:NamespaceID"`
	Services    []*Service    `json:"services,omitempty" gorm:"foreignKey:NamespaceID"`
	Ingress     []*Ingress    `json:"ingress,omitempty" gorm:"foreignKey:NamespaceID"`
	Domains     []*Domain     `json:"domains,omitempty" gorm:"foreignKey:NamespaceID"`
}
