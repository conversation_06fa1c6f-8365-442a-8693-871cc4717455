package domain

type IngressSpec struct {
	BaseModel
	Host      string `json:"host" gorm:"not null"`
	Path      string `json:"path" gorm:"not null"`
	Port      uint64 `json:"port" gorm:"not null"`
	ServiceID uint64 `json:"service_id" gorm:"not null"`
	IngressID uint64 `json:"ingress_id" gorm:"not null"`

	// Relationships
	Service *Service `json:"service,omitempty" gorm:"foreignKey:ServiceID"`
	Ingress *Ingress `json:"ingress,omitempty" gorm:"foreignKey:IngressID"`
}
