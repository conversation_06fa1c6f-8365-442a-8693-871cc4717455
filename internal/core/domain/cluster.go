package domain

type Cluster struct {
	BaseModel
	Name          string `json:"name" gorm:"not null"`
	Region        string `json:"region" gorm:"not null"`
	PoolName      string `json:"pool_name" gorm:"not null"`
	Size          string `json:"size" gorm:"not null"`
	NodeCount     uint64 `json:"node_count" gorm:"not null"`
	LoadBalanceIP string `json:"load_balance_ip"`
	IsSelf        bool   `json:"is_self" gorm:"not null default:false"`
	WorkspaceID   uint64 `json:"workspace_id"`
	StatusID      uint64 `json:"status_id"`

	// Relationships
	Workspace  *Workspace    `json:"workspace,omitempty" gorm:"foreignKey:WorkspaceID"`
	Status     *ServerStatus `json:"status,omitempty" gorm:"foreignKey:StatusID"`
	Namespaces []Namespace   `json:"namespaces,omitempty" gorm:"foreignKey:ClusterID"`
}
