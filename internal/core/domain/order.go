package domain

type Order struct {
	BaseModel
	Name        string `json:"name" gorm:"not null"`
	Code        string `json:"code" gorm:"not null"`
	Description string `json:"description" gorm:"not null"`
	LineCode    string `json:"line_code" gorm:"not null default:'-'"`
	IsConfirmed bool   `json:"is_confirmed" gorm:"not null default:false"`
	UserID      uint64 `json:"user_id" gorm:"not null"`
	TemplateID  uint64 `json:"template_id" gorm:"not null"`
	Duration    uint64 `json:"duration" gorm:"not null default:0"`

	// Relationships
	User            *User            `json:"user,omitempty" gorm:"foreignKey:UserID"`
	Template        *Namespace       `json:"template,omitempty" gorm:"foreignKey:TemplateID"`
	OrderDomains    []OrderDomain    `json:"order_domains,omitempty" gorm:"foreignKey:OrderID"`
	OrderNamespaces []OrderNamespace `json:"order_namespaces,omitempty" gorm:"foreignKey:OrderID"`
}
