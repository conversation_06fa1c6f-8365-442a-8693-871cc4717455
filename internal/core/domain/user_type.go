package domain

type UserType struct {
	BaseModel
	ID          uint64 `json:"id" gorm:"primarykey"`
	Name        string `json:"name" gorm:"uniqueIndex;not null"`
	Description string `json:"description"`
	IsActive    bool   `json:"is_active" gorm:"default:true"`
	IsAdmin     bool   `json:"is_admin" gorm:"default:false"`
	IsMember    bool   `json:"is_member" gorm:"default:false"`
	IsSale      bool   `json:"is_sale" gorm:"default:false"`
}

const (
	UserTypeAdmin  = "admin"
	UserTypeMember = "member"
	UserTypeSale   = "sale"
)
