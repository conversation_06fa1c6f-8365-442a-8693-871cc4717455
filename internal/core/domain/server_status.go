package domain

type ServerStatusType string

const (
	ServerStatusUnpublished ServerStatusType = "unpublished"
	ServerStatusCreating    ServerStatusType = "creating"
	ServerStatusActive      ServerStatusType = "active"
	ServerStatusStopped     ServerStatusType = "stopped"
	ServerStatusUpdating    ServerStatusType = "updating"
	ServerStatusMaintenance ServerStatusType = "maintenance"
	ServerStatusDeleting    ServerStatusType = "deleting"
	ServerStatusError       ServerStatusType = "error"
	ServerStatusDestroyed   ServerStatusType = "destroyed"
)

type ServerStatus struct {
	BaseModel
	Name ServerStatusType `json:"name" gorm:"not null"`
}
