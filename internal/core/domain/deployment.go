package domain

import "time"

type Deployment struct {
	BaseModel
	Name           string     `json:"name" gorm:"not null"`
	Image          string     `json:"image" gorm:"not null"`
	ContainerPort  uint64     `json:"container_port" gorm:"not null"`
	Replicas       uint64     `json:"replicas" gorm:"not null;default:1"`
	LastDeployedAt *time.Time `json:"last_deployed_at,omitempty" gorm:"default:null"`
	NamespaceID    uint64     `json:"namespace_id" gorm:"not null"`
	StatusID       uint64     `json:"status_id" gorm:"not null"`

	// Relationships
	Namespace    *Namespace     `json:"namespace,omitempty" gorm:"foreignKey:NamespaceID"`
	Status       *ServerStatus  `json:"status,omitempty" gorm:"foreignKey:StatusID"`
	Environments []*Environment `json:"environments,omitempty" gorm:"foreignKey:DeploymentID"`
}
