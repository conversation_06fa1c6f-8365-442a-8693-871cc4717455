package domain

type User struct {
	BaseModel
	Name       string `json:"name" gorm:"not null"`
	Email      string `json:"email"`
	Password   string `json:"-"`
	UserTypeID uint64 `json:"user_type_id" gorm:"not null"`

	// Relationships
	UserType   *UserType   `json:"user_type,omitempty" gorm:"foreignKey:UserTypeID"`
	Jobs       []Job       `json:"jobs,omitempty" gorm:"foreignKey:UserID"`
	Workspaces []Workspace `json:"workspaces,omitempty" gorm:"foreignKey:UserID"`
}
