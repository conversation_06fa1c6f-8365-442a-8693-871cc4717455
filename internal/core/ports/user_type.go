package ports

import "ops-api/internal/core/domain"

// UserTypeFilter represents filters for querying user types
type UserTypeFilter struct {
	IsActive *bool
	IsAdmin  *bool
	IsMember *bool
	IsSales  *bool
	Name     *string
}

type UserTypeRepository interface {
	Insert(userType *domain.UserType) error
	FindAll(filter *UserTypeFilter) ([]*domain.UserType, error)
	FindByID(id uint) (*domain.UserType, error)
	Update(userType *domain.UserType) error
	Delete(id uint) error
}

type UserTypeService interface {
	Create(name, description string, isActive, isAdmin, isMember, isSale bool) (*domain.UserType, error)
	GetByID(id uint) (*domain.UserType, error)
	GetByName(name string) (*domain.UserType, error)
	GetAll(filter *UserTypeFilter) ([]*domain.UserType, error)
	Edit(id uint, name, description string, isActive, isAdmin, isMember, isSale bool) (*domain.UserType, error)
	Remove(id uint) error
}
