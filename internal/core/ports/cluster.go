package ports

import "ops-api/internal/core/domain"

// ClusterFilter represents filters for querying clusters
type ClusterFilter struct {
	Name        *string
	Region      *string
	WorkspaceID *uint64
}

type ClusterRepository interface {
	Insert(cluster *domain.Cluster) error
	FindAll(filter *ClusterFilter) ([]*domain.Cluster, error)
	FindByID(id, namespaceId uint64) (*domain.Cluster, error)
	FindByName(name string) (*domain.Cluster, error)
	Update(cluster *domain.Cluster) error
	Delete(id uint64) error
}

type ClusterService interface {
	Create(name, region, poolName, size string, nodeCount, workspaceID uint64) (*domain.Cluster, error)
	CreateWithUserValidation(userID uint64, name, region, poolName, size string, nodeCount, workspaceID uint64) (*domain.Cluster, error)
	CreateSelfCluster(userID uint64, workspaceID uint64) (*domain.Cluster, error)
	GetAll(filter *ClusterFilter) ([]*domain.Cluster, error)
	GetAllByUserWorkspaces(userID uint64, filter *ClusterFilter) ([]*domain.Cluster, error)
	GetByID(id uint64) (*domain.Cluster, error)
	Update(id uint64, name, region, poolName, size string, nodeCount, workspaceID, statusID uint64) (*domain.Cluster, error)
	UpdateLoadBalancerIP(id uint64, loadBalancerIP string) (*domain.Cluster, error)
	Delete(id uint64) error
}
