package ports

import "ops-api/internal/core/dto"

type CloudflareService interface {
	GetAllZones() (*dto.CloudflareResponse, error)
	GetZonesWithAPI(page int, name string, perPage int, accountName string) (*dto.CloudflareAPIZonesResponse, error)
	EnableAlwaysUseHTTPS(zoneID string) (*dto.CloudflareAPISettingResponse, error)
	ListPageRules(zoneID string) (*dto.CloudflarePageRulesResponse, error)
	CreatePageRule(zoneID string, request *dto.CloudflarePageRuleCreateRequest) (*dto.CloudflarePageRuleResponse, error)
	DeletePageRule(zoneID, pageRuleID string) (*dto.CloudflarePageRuleResponse, error)
}
