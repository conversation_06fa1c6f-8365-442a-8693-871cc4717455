package ports

import (
	"context"
)

// PoolService defines the interface for the shared worker pool service
// This service manages a single worker pool with controlled concurrency
type PoolService interface {
	// Start initializes and starts the worker pool
	Start() error

	// Stop gracefully shuts down the worker pool
	Stop() error

	// Submit submits a task to the worker pool for execution
	// The task will be executed asynchronously by the single worker
	Submit(task func()) error

	// SubmitWithContext submits a task with context for cancellation support
	SubmitWithContext(ctx context.Context, task func()) error

	// IsRunning returns true if the pool is currently running
	IsRunning() bool

	// GetPoolSize returns the configured pool size (should be 1)
	GetPoolSize() int

	// GetRunningWorkers returns the number of currently running workers
	GetRunningWorkers() int

	// GetWaitingTasks returns the number of tasks waiting in the queue
	GetWaitingTasks() int
}
