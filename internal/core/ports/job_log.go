package ports

import "ops-api/internal/core/domain"

// JobLogFilter represents filters for querying job logs
type JobLogFilter struct {
	JobID *uint64
	Name  *string
}

type JobLogRepository interface {
	Insert(jobLog *domain.JobLog) error
	FindAll(filter *JobLogFilter) ([]*domain.JobLog, error)
	FindByID(id uint64) (*domain.JobLog, error)
	FindByJobID(jobID uint64) ([]*domain.JobLog, error)
	Update(jobLog *domain.JobLog) error
	Delete(id uint64) error
}

type JobLogService interface {
	CreateJobLog(jobID uint64, name, description string) (*domain.JobLog, error)
	GetJobLogsByJobID(jobID uint64) ([]*domain.JobLog, error)
	GetJobLogByID(id uint64) (*domain.JobLog, error)
	AutoCreateJobLog(jobID uint64, eventName, eventDescription string) (*domain.JobLog, error)
}
