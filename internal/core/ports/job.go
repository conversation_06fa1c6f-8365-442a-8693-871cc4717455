package ports

import "ops-api/internal/core/domain"

// <PERSON><PERSON><PERSON><PERSON> represents filters for querying jobs
type JobFilter struct {
	Name        *string
	UserID      *uint64 // For filtering "my jobs"
	JobStatusID *uint64
	EventID     *uint64           // For filtering by event
	Event       *domain.JobEvent  // For filtering by event type
	Action      *domain.JobAction // For filtering by action type
}

type JobRepository interface {
	Insert(job *domain.Job) error
	FindAll(filter *JobFilter) ([]*domain.Job, error)
	FindByID(id uint64) (*domain.Job, error)
	Update(job *domain.Job) error
	Delete(id uint64) error
}

type JobService interface {
	CreateJob(userID uint64, name, description string, jobStatusID uint64, eventID *uint64, event domain.JobEvent, action domain.JobAction) (*domain.Job, error)
	GetAllJobs(filter *JobFilter) ([]*domain.Job, error)
	GetJobByID(id uint64) (*domain.Job, error)
	GetMyJobs(userID uint64) ([]*domain.Job, error)
	UpdateJob(id uint64, userID uint64, name, description string, jobStatusID uint64, eventID *uint64, event domain.JobEvent, action domain.JobAction) (*domain.Job, error)
}
