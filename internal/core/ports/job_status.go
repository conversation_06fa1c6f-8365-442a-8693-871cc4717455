package ports

import "ops-api/internal/core/domain"

// JobStatusFilter represents filters for querying job statuses
type JobStatusFilter struct {
	Name *string
}

type JobStatusRepository interface {
	Insert(jobStatus *domain.JobStatus) error
	FindAll(filter *JobStatusFilter) ([]*domain.JobStatus, error)
	FindByID(id uint) (*domain.JobStatus, error)
	Update(jobStatus *domain.JobStatus) error
	Delete(id uint) error
}

type JobStatusService interface {
	Create(name string) (*domain.JobStatus, error)
	GetByID(id uint) (*domain.JobStatus, error)
	GetByName(name string) (*domain.JobStatus, error)
	GetAll(filter *JobStatusFilter) ([]*domain.JobStatus, error)
	Edit(id uint, name string) (*domain.JobStatus, error)
	Remove(id uint) error
}
