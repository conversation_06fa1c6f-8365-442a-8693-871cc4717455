package ports

import "ops-api/internal/core/domain"

// Ingress<PERSON>ilter represents filters for querying ingresses
type IngressFilter struct {
	Name        *string
	NamespaceID *uint64
}

type IngressRepository interface {
	Insert(ingress *domain.Ingress) error
	FindAll(filter *IngressFilter) ([]*domain.Ingress, error)
	FindByID(id uint64) (*domain.Ingress, error)
	Update(ingress *domain.Ingress) error
	Delete(id uint64) error
}

type IngressService interface {
	Create(name, class string, namespaceID uint64) (*domain.Ingress, error)
	GetAll(filter *IngressFilter) ([]*domain.Ingress, error)
	GetByID(id uint64) (*domain.Ingress, error)
	Update(id uint64, name, class string, statusID uint64) (*domain.Ingress, error)
	UpdateStatus(id uint64, statusID uint64) (*domain.Ingress, error)
	Delete(id uint64) error
}
