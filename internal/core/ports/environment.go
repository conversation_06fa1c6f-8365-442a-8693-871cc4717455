package ports

import "ops-api/internal/core/domain"

// EnvironmentFilter represents filters for querying environments
type EnvironmentFilter struct {
	Name         *string
	DeploymentID *uint64
}

type EnvironmentRepository interface {
	Insert(environment *domain.Environment) error
	FindAll(filter *EnvironmentFilter) ([]*domain.Environment, error)
	FindByID(id uint64) (*domain.Environment, error)
	Update(environment *domain.Environment) error
	Delete(id uint64) error
}

type EnvironmentService interface {
	Create(name, value string, deploymentID uint64) (*domain.Environment, error)
	GetAll(filter *EnvironmentFilter) ([]*domain.Environment, error)
	GetByID(id uint64) (*domain.Environment, error)
	Update(id uint64, name, value string, deploymentID uint64) (*domain.Environment, error)
	Delete(id uint64) error
}
