package ports

import "ops-api/internal/core/domain"

// WorkspaceFilter represents filters for querying workspaces
type WorkspaceFilter struct {
	Name   *string
	UserID *uint64
}

type WorkspaceRepository interface {
	Insert(workspace *domain.Workspace) error
	FindAll(filter *WorkspaceFilter) ([]*domain.Workspace, error)
	FindByID(id uint64) (*domain.Workspace, error)
	FindByUserID(userID uint64) ([]*domain.Workspace, error)
	Update(workspace *domain.Workspace) error
	Delete(id uint64) error
}

type WorkspaceService interface {
	Create(userID uint64, name, description string) (*domain.Workspace, error)
	GetAll(filter *WorkspaceFilter) ([]*domain.Workspace, error)
	GetByID(id uint64) (*domain.Workspace, error)
	GetByUserID(userID uint64) ([]*domain.Workspace, error)
	Update(id, userID uint64, name, description string) (*domain.Workspace, error)
	Delete(id, userID uint64) error
}
