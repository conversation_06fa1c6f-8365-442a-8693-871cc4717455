package ports

import "ops-api/internal/core/domain"

// OrderNamespaceFilter represents filters for querying order namespaces
type OrderNamespaceFilter struct {
	OrderID     *uint64
	NamespaceID *uint64
	UserID      *uint64 // For filtering by user access
}

type OrderNamespaceRepository interface {
	Insert(orderNamespace *domain.OrderNamespace) error
	FindAll(filter *OrderNamespaceFilter) ([]*domain.OrderNamespace, error)
	FindByID(id uint64) (*domain.OrderNamespace, error)
	Update(orderNamespace *domain.OrderNamespace) error
	Delete(id uint64) error
}

type OrderNamespaceService interface {
	Create(orderID, namespaceID uint64) (*domain.OrderNamespace, error)
	GetAll(filter *OrderNamespaceFilter) ([]*domain.OrderNamespace, error)
	GetAllByUser(userID uint64) ([]*domain.OrderNamespace, error)
	GetByID(id uint64) (*domain.OrderNamespace, error)
	Delete(id uint64) error
}
