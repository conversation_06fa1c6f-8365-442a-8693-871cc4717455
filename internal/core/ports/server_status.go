package ports

import "ops-api/internal/core/domain"

type ServerStatusRepository interface {
	FindAll() ([]*domain.ServerStatus, error)
	FindByID(id uint64) (*domain.ServerStatus, error)
	FindByName(name string) (*domain.ServerStatus, error)
	Create(serverStatus *domain.ServerStatus) (*domain.ServerStatus, error)
}

type ServerStatusService interface {
	GetAll() ([]*domain.ServerStatus, error)
	GetByID(id uint64) (*domain.ServerStatus, error)
	GetByName(name string) (*domain.ServerStatus, error)
	Create(name string) (*domain.ServerStatus, error)
}
