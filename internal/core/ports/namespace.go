package ports

import "ops-api/internal/core/domain"

// NamespaceFilter represents filters for querying namespaces
type NamespaceFilter struct {
	Name      *string
	Slug      *string
	ClusterID *uint64
	IsActive  *bool
	Type      *domain.NamespaceType
}

type NamespaceRepository interface {
	Insert(namespace *domain.Namespace) error
	FindAll(filter *NamespaceFilter) ([]*domain.Namespace, error)
	FindByID(id uint64) (*domain.Namespace, error)
	Update(namespace *domain.Namespace) error
	Delete(id uint64) error
	CountByType(namespaceType domain.NamespaceType) (int64, error)
}

type NamespaceService interface {
	Create(name, slug string, isActive bool, Type domain.NamespaceType, clusterID uint64) (*domain.Namespace, error)
	GetAll(filter *NamespaceFilter) ([]*domain.Namespace, error)
	GetAllByUserWorkspaces(userID uint64, filter *NamespaceFilter) ([]*domain.Namespace, error)
	GetByID(id uint64) (*domain.Namespace, error)
	Update(id uint64, name, slug string, isActive bool, Type domain.NamespaceType, clusterID uint64) (*domain.Namespace, error)
	Delete(id uint64) error
}
