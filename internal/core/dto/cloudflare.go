package dto

import (
	"encoding/json"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"
)

// CloudflareAccount represents a Cloudflare account
type CloudflareAccount struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

// CloudflareDNSRecord represents a DNS record in Cloudflare
type CloudflareDNSRecord struct {
	ID      string `json:"id"`
	Name    string `json:"name"`
	Type    string `json:"type"`
	Content string `json:"content"`
	Proxied bool   `json:"proxied"`
}

// CloudflareZone represents a Cloudflare zone with DNS records
type CloudflareZone struct {
	ZoneID     string                `json:"zone_id"`
	ZoneName   string                `json:"zone_name"`
	ZoneStatus string                `json:"zone_status"`
	Account    CloudflareAccount     `json:"account"`
	DNSRecords []CloudflareDNSRecord `json:"dns_records"`
}

// CloudflareSummary represents the summary of zones and record counts
type CloudflareSummary struct {
	TotalDomains          int            `json:"total_domains"`
	ZonesWithRecordCounts map[string]int `json:"zones_with_record_counts"`
}

// CloudflareResponse represents the complete response structure
type CloudflareResponse struct {
	Summary             CloudflareSummary `json:"summary"`
	ZonesWithDNSRecords []CloudflareZone  `json:"zones_with_dns_records"`
}

// CloudflareAPIZone represents a zone object from the Cloudflare API
type CloudflareAPIZone struct {
	ID                  string                   `json:"id"`
	Name                string                   `json:"name"`
	Status              string                   `json:"status"`
	Paused              bool                     `json:"paused"`
	Type                string                   `json:"type"`
	DevelopmentMode     int                      `json:"development_mode"`
	NameServers         []string                 `json:"name_servers"`
	OriginalNameServers []string                 `json:"original_name_servers"`
	OriginalRegistrar   string                   `json:"original_registrar"`
	OriginalDNSHost     string                   `json:"original_dnshost"`
	ModifiedOn          time.Time                `json:"modified_on"`
	CreatedOn           time.Time                `json:"created_on"`
	ActivatedOn         *time.Time               `json:"activated_on"`
	Meta                CloudflareAPIZoneMeta    `json:"meta"`
	Owner               CloudflareAPIZoneOwner   `json:"owner"`
	Account             CloudflareAPIZoneAccount `json:"account"`
	Permissions         []string                 `json:"permissions"`
	Plan                CloudflareAPIZonePlan    `json:"plan"`
	AlwaysUseHTTPS      string                   `json:"always_use_https"`
}

// CloudflareAPIZoneMeta represents zone metadata
type CloudflareAPIZoneMeta struct {
	Step                    int  `json:"step"`
	CustomCertificateQuota  int  `json:"custom_certificate_quota"`
	PageRuleQuota           int  `json:"page_rule_quota"`
	PhishingDetected        bool `json:"phishing_detected"`
	MultipleRailgunsAllowed bool `json:"multiple_railguns_allowed"`
}

// CloudflareAPIZoneOwner represents zone owner information
type CloudflareAPIZoneOwner struct {
	ID    string `json:"id"`
	Type  string `json:"type"`
	Email string `json:"email"`
}

// CloudflareAPIZoneAccount represents zone account information
type CloudflareAPIZoneAccount struct {
	ID   string `json:"id"`
	Name string `json:"name"`
}

// CloudflareAPIZonePlan represents zone plan information
type CloudflareAPIZonePlan struct {
	ID                string `json:"id"`
	Name              string `json:"name"`
	Price             int    `json:"price"`
	Currency          string `json:"currency"`
	Frequency         string `json:"frequency"`
	IsSubscribed      bool   `json:"is_subscribed"`
	CanSubscribe      bool   `json:"can_subscribe"`
	LegacyID          string `json:"legacy_id"`
	LegacyDiscount    bool   `json:"legacy_discount"`
	ExternallyManaged bool   `json:"externally_managed"`
}

// CloudflareAPIResultInfo represents pagination metadata
type CloudflareAPIResultInfo struct {
	Page       int `json:"page"`
	PerPage    int `json:"per_page"`
	TotalPages int `json:"total_pages"`
	Count      int `json:"count"`
	TotalCount int `json:"total_count"`
}

// CloudflareAPIZonesResponse represents the response from Cloudflare zones API
type CloudflareAPIZonesResponse struct {
	Result     []CloudflareAPIZone     `json:"result"`
	ResultInfo CloudflareAPIResultInfo `json:"result_info"`
	Success    bool                    `json:"success"`
	Errors     []CloudflareAPIError    `json:"errors"`
	Messages   []CloudflareAPIMessage  `json:"messages"`
}

// CloudflareAPIError represents an error from the Cloudflare API
type CloudflareAPIError struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

// CloudflareAPIMessage represents a message from the Cloudflare API
type CloudflareAPIMessage struct {
	Code    int    `json:"code"`
	Message string `json:"message"`
}

// CloudflareAPISettingResult represents the result object from a zone setting API call
type CloudflareAPISettingResult struct {
	ID         string    `json:"id"`
	Value      string    `json:"value"`
	ModifiedOn time.Time `json:"modified_on"`
	Editable   bool      `json:"editable"`
}

// CloudflareAPISettingResponse represents the response from a zone setting API call
type CloudflareAPISettingResponse struct {
	Result     CloudflareAPISettingResult `json:"result"`
	Success    bool                       `json:"success"`
	Errors     []CloudflareAPIError       `json:"errors"`
	Messages   []CloudflareAPIMessage     `json:"messages"`
	ResultInfo interface{}                `json:"result_info"`
}

// CloudflarePageRuleTarget represents a target for a page rule
type CloudflarePageRuleTarget struct {
	Target     string                 `json:"target"`
	Constraint map[string]interface{} `json:"constraint"`
}

// CloudflarePageRuleAction represents an action for a page rule
type CloudflarePageRuleAction struct {
	ID    string      `json:"id"`
	Value interface{} `json:"value,omitempty"`
}

// CloudflarePageRule represents a Cloudflare page rule
type CloudflarePageRule struct {
	ID         string                     `json:"id"`
	Targets    []CloudflarePageRuleTarget `json:"targets"`
	Actions    []CloudflarePageRuleAction `json:"actions"`
	Priority   int                        `json:"priority"`
	Status     string                     `json:"status"`
	ModifiedOn time.Time                  `json:"modified_on"`
	CreatedOn  time.Time                  `json:"created_on"`
}

// CloudflarePageRulesResponse represents the response from page rules API
type CloudflarePageRulesResponse struct {
	Result     []CloudflarePageRule    `json:"result"`
	ResultInfo CloudflareAPIResultInfo `json:"result_info"`
	Success    bool                    `json:"success"`
	Errors     []CloudflareAPIError    `json:"errors"`
	Messages   []CloudflareAPIMessage  `json:"messages"`
}

// CloudflarePageRuleResponse represents the response from a single page rule API call
type CloudflarePageRuleResponse struct {
	Result     CloudflarePageRule     `json:"result"`
	Success    bool                   `json:"success"`
	Errors     []CloudflareAPIError   `json:"errors"`
	Messages   []CloudflareAPIMessage `json:"messages"`
	ResultInfo interface{}            `json:"result_info"`
}

// CloudflarePageRuleCreateRequest represents the request body for creating a page rule
type CloudflarePageRuleCreateRequest struct {
	Targets  []CloudflarePageRuleTarget `json:"targets"`
	Actions  []CloudflarePageRuleAction `json:"actions"`
	Priority *int                       `json:"priority,omitempty"`
	Status   string                     `json:"status,omitempty"`
}

// TerraformOutput represents the structure of terraform output JSON
type TerraformOutput struct {
	Summary struct {
		Value struct {
			TotalDomains          int            `json:"total_domains"`
			ZonesWithRecordCounts map[string]int `json:"zones_with_record_counts"`
		} `json:"value"`
	} `json:"summary"`
	ZonesWithDNSRecords struct {
		Value []struct {
			ZoneID     string `json:"zone_id"`
			ZoneName   string `json:"zone_name"`
			ZoneStatus string `json:"zone_status"`
			Account    struct {
				ID   string `json:"id"`
				Name string `json:"name"`
			} `json:"account"`
			DNSRecords []struct {
				ID      string `json:"id"`
				Name    string `json:"name"`
				Type    string `json:"type"`
				Content string `json:"content"`
				Proxied bool   `json:"proxied"`
			} `json:"dns_records"`
		} `json:"value"`
	} `json:"zones_with_dns_records"`
}

// NewCloudflareResponseFromJSON creates a CloudflareResponse from JSON terraform output
func NewCloudflareResponseFromJSON(jsonOutput string) (*CloudflareResponse, error) {
	var tfOutput TerraformOutput
	if err := json.Unmarshal([]byte(jsonOutput), &tfOutput); err != nil {
		fmt.Printf("Failed to parse terraform output JSON: %v\n", err)
		fmt.Printf("JSON content: %s\n", jsonOutput)
		return nil, err
	}

	// Convert to CloudflareResponse
	response := &CloudflareResponse{
		Summary: CloudflareSummary{
			TotalDomains:          tfOutput.Summary.Value.TotalDomains,
			ZonesWithRecordCounts: tfOutput.Summary.Value.ZonesWithRecordCounts,
		},
		ZonesWithDNSRecords: make([]CloudflareZone, len(tfOutput.ZonesWithDNSRecords.Value)),
	}

	// Convert zones
	for i, zone := range tfOutput.ZonesWithDNSRecords.Value {
		response.ZonesWithDNSRecords[i] = CloudflareZone{
			ZoneID:     zone.ZoneID,
			ZoneName:   zone.ZoneName,
			ZoneStatus: zone.ZoneStatus,
			Account: CloudflareAccount{
				ID:   zone.Account.ID,
				Name: zone.Account.Name,
			},
			DNSRecords: make([]CloudflareDNSRecord, len(zone.DNSRecords)),
		}

		// Convert DNS records
		for j, record := range zone.DNSRecords {
			response.ZonesWithDNSRecords[i].DNSRecords[j] = CloudflareDNSRecord{
				ID:      record.ID,
				Name:    record.Name,
				Type:    record.Type,
				Content: record.Content,
				Proxied: record.Proxied,
			}
		}
	}

	return response, nil
}

// NewCloudflareResponseFromHCL creates a CloudflareResponse from HCL terraform output
func NewCloudflareResponseFromHCL(output string) (*CloudflareResponse, error) {
	response := &CloudflareResponse{
		Summary: CloudflareSummary{
			ZonesWithRecordCounts: make(map[string]int),
		},
		ZonesWithDNSRecords: []CloudflareZone{},
	}

	// Extract total_domains
	if totalDomainsMatch := regexp.MustCompile(`"total_domains"\s*=\s*(\d+)`).FindStringSubmatch(output); len(totalDomainsMatch) > 1 {
		if totalDomains, err := strconv.Atoi(totalDomainsMatch[1]); err == nil {
			response.Summary.TotalDomains = totalDomains
		}
	}

	// Extract zones_with_record_counts
	zonesWithCountsRegex := regexp.MustCompile(`"zones_with_record_counts"\s*=\s*\{([^}]+)\}`)
	if zonesWithCountsMatch := zonesWithCountsRegex.FindStringSubmatch(output); len(zonesWithCountsMatch) > 1 {
		countEntries := zonesWithCountsMatch[1]
		entryRegex := regexp.MustCompile(`"([^"]+)"\s*=\s*(\d+)`)
		for _, match := range entryRegex.FindAllStringSubmatch(countEntries, -1) {
			if len(match) > 2 {
				if count, err := strconv.Atoi(match[2]); err == nil {
					response.Summary.ZonesWithRecordCounts[match[1]] = count
				}
			}
		}
	}

	// Extract zones_with_dns_records array
	zonesArrayRegex := regexp.MustCompile(`zones_with_dns_records\s*=\s*\[(.*?)\]`)
	if zonesArrayMatch := zonesArrayRegex.FindStringSubmatch(output); len(zonesArrayMatch) > 1 {
		zonesContent := zonesArrayMatch[1]

		// Split zones by looking for zone objects
		zoneRegex := regexp.MustCompile(`\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}`)
		zoneMatches := zoneRegex.FindAllString(zonesContent, -1)

		for _, zoneStr := range zoneMatches {
			zone := parseZoneFromHCL(zoneStr)
			if zone != nil {
				response.ZonesWithDNSRecords = append(response.ZonesWithDNSRecords, *zone)
			}
		}
	}

	return response, nil
}

// parseZoneFromHCL parses a single zone from HCL format
func parseZoneFromHCL(zoneStr string) *CloudflareZone {
	zone := &CloudflareZone{
		Account:    CloudflareAccount{},
		DNSRecords: []CloudflareDNSRecord{},
	}

	// Extract zone fields
	if match := regexp.MustCompile(`"zone_id"\s*=\s*"([^"]+)"`).FindStringSubmatch(zoneStr); len(match) > 1 {
		zone.ZoneID = match[1]
	}
	if match := regexp.MustCompile(`"zone_name"\s*=\s*"([^"]+)"`).FindStringSubmatch(zoneStr); len(match) > 1 {
		zone.ZoneName = match[1]
	}
	if match := regexp.MustCompile(`"zone_status"\s*=\s*"([^"]+)"`).FindStringSubmatch(zoneStr); len(match) > 1 {
		zone.ZoneStatus = match[1]
	}

	// Extract account info
	accountRegex := regexp.MustCompile(`"account"\s*=\s*\{([^}]+)\}`)
	if accountMatch := accountRegex.FindStringSubmatch(zoneStr); len(accountMatch) > 1 {
		accountStr := accountMatch[1]
		if match := regexp.MustCompile(`"id"\s*=\s*"([^"]+)"`).FindStringSubmatch(accountStr); len(match) > 1 {
			zone.Account.ID = match[1]
		}
		if match := regexp.MustCompile(`"name"\s*=\s*"([^"]+)"`).FindStringSubmatch(accountStr); len(match) > 1 {
			zone.Account.Name = match[1]
		}
	}

	// Extract DNS records
	dnsRecordsRegex := regexp.MustCompile(`"dns_records"\s*=\s*\[(.*?)\]`)
	if dnsRecordsMatch := dnsRecordsRegex.FindStringSubmatch(zoneStr); len(dnsRecordsMatch) > 1 {
		recordsContent := dnsRecordsMatch[1]

		// Parse individual records
		recordRegex := regexp.MustCompile(`\{[^{}]*\}`)
		recordMatches := recordRegex.FindAllString(recordsContent, -1)

		for _, recordStr := range recordMatches {
			record := parseRecordFromHCL(recordStr)
			if record != nil {
				zone.DNSRecords = append(zone.DNSRecords, *record)
			}
		}
	}

	return zone
}

// parseRecordFromHCL parses a single DNS record from HCL format
func parseRecordFromHCL(recordStr string) *CloudflareDNSRecord {
	record := &CloudflareDNSRecord{}

	if match := regexp.MustCompile(`"id"\s*=\s*"([^"]+)"`).FindStringSubmatch(recordStr); len(match) > 1 {
		record.ID = match[1]
	}
	if match := regexp.MustCompile(`"name"\s*=\s*"([^"]+)"`).FindStringSubmatch(recordStr); len(match) > 1 {
		record.Name = match[1]
	}
	if match := regexp.MustCompile(`"type"\s*=\s*"([^"]+)"`).FindStringSubmatch(recordStr); len(match) > 1 {
		record.Type = match[1]
	}
	if match := regexp.MustCompile(`"content"\s*=\s*"([^"]+)"`).FindStringSubmatch(recordStr); len(match) > 1 {
		record.Content = match[1]
	}
	if match := regexp.MustCompile(`"proxied"\s*=\s*(true|false)`).FindStringSubmatch(recordStr); len(match) > 1 {
		record.Proxied = match[1] == "true"
	}

	return record
}

// ExtractOutputSection extracts the output section from terraform apply output
func ExtractOutputSection(output string) string {
	lines := strings.Split(output, "\n")
	inOutputSection := false
	var outputLines []string

	for _, line := range lines {
		// Look for "Outputs:" section
		if strings.Contains(line, "Outputs:") {
			inOutputSection = true
			continue
		}

		// If we're in output section and hit an empty line or new section, stop
		if inOutputSection {
			if strings.TrimSpace(line) == "" {
				break
			}
			outputLines = append(outputLines, line)
		}
	}

	return strings.Join(outputLines, "\n")
}

// NewCloudflareResponseFromTerraformOutput parses terraform output and creates CloudflareResponse
// This is the main parsing method that tries different parsing strategies
func NewCloudflareResponseFromTerraformOutput(output string) (*CloudflareResponse, error) {
	// First try to parse as JSON
	if response, err := NewCloudflareResponseFromJSON(output); err == nil {
		return response, nil
	}

	// If that fails, try to extract output section and parse as JSON
	outputSection := ExtractOutputSection(output)
	if outputSection != "" {
		if response, err := NewCloudflareResponseFromJSON(outputSection); err == nil {
			return response, nil
		}
	}

	// Fallback to HCL parsing
	fmt.Println("Falling back to HCL parsing of terraform output")
	return NewCloudflareResponseFromHCL(output)
}
