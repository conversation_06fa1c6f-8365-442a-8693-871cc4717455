package dto

import (
	"ops-api/internal/core/domain"
)

type ServerStatusResponse struct {
	ID   uint64                  `json:"id"`
	Name domain.ServerStatusType `json:"name"`
}

type ServerStatusRelationResponse struct {
	ID   uint64                  `json:"id"`
	Name domain.ServerStatusType `json:"name"`
}

func ToServerStatusRelationDTO(d *domain.ServerStatus) *ServerStatusRelationResponse {
	if d == nil {
		return nil
	}
	return &ServerStatusRelationResponse{
		ID:   d.ID,
		Name: d.Name,
	}
}
