package dto

import (
	"ops-api/internal/core/domain"
	"time"
)

// CreateProjectRequest represents a comprehensive project creation request
type CreateProjectRequest struct {
	// Namespace details
	Name      string               `json:"name" validate:"required,min=2,max=50"`
	Slug      string               `json:"slug" validate:"required,min=2,max=50"`
	IsActive  bool                 `json:"is_active"`
	Type      domain.NamespaceType `json:"type" validate:"required"`
	ClusterID uint64               `json:"cluster_id" validate:"required"`

	// Deployments with their environments and services
	Deployments []CreateProjectDeploymentRequest `json:"deployments" validate:"required,min=1"`
}

// CreateProjectWithTemplateRequest represents a request to create a project from a template
type CreateProjectWithTemplateRequest struct {
	TemplateID uint64 `json:"template_id" validate:"required"`
	Name       string `json:"name" validate:"required,min=2,max=50"`
}

// CreateProjectFromOrderRequest represents a request to create a project from an order
type CreateProjectFromOrderRequest struct {
	OrderID uint64 `json:"order_id" validate:"required"`
}

// CreateProjectDeploymentRequest represents a deployment within a project
type CreateProjectDeploymentRequest struct {
	Name          string                            `json:"name" validate:"required,min=2,max=50"`
	Image         string                            `json:"image" validate:"required"`
	ContainerPort uint64                            `json:"container_port" validate:"required,min=1,max=65535"`
	Replicas      uint64                            `json:"replicas" validate:"min=1,max=100"`
	Environments  []CreateProjectEnvironmentRequest `json:"environments,omitempty"`
	Services      []CreateProjectServiceRequest     `json:"services" validate:"required,min=1"`
}

// CreateProjectEnvironmentRequest represents environment variables
type CreateProjectEnvironmentRequest struct {
	Name  string `json:"name" validate:"required"`
	Value string `json:"value" validate:"required"`
}

// CreateProjectServiceRequest represents a service with ingress spec
type CreateProjectServiceRequest struct {
	Name        string                          `json:"name" validate:"required,min=2,max=50"`
	Port        string                          `json:"port" validate:"required"`
	TargetPort  string                          `json:"target_port" validate:"required"`
	Type        string                          `json:"type" validate:"required,oneof=ClusterIP NodePort LoadBalancer"`
	IngressSpec CreateProjectIngressSpecRequest `json:"ingress_spec"`
}

// CreateProjectIngressSpecRequest represents ingress specification for a service
type CreateProjectIngressSpecRequest struct {
	Host string `json:"host" validate:"required"`
	Path string `json:"path" validate:"required"`
	Port uint64 `json:"port" validate:"required,min=1,max=65535"`
}

// UpdateProjectRequest represents an update request for a project
type UpdateProjectRequest struct {
	// Namespace details
	Name      string               `json:"name" validate:"required,min=2,max=50"`
	Slug      string               `json:"slug" validate:"required,min=2,max=50"`
	IsActive  bool                 `json:"is_active"`
	Type      domain.NamespaceType `json:"type" validate:"required"`
	ClusterID uint64               `json:"cluster_id" validate:"required"`

	// Deployments with their environments and services
	Deployments []CreateProjectDeploymentRequest `json:"deployments" validate:"required,min=1"`
}

// Response DTOs for project operations
type CreateProjectDeploymentResponse struct {
	ID            uint64                             `json:"id"`
	CreatedAt     time.Time                          `json:"created_at"`
	UpdatedAt     time.Time                          `json:"updated_at"`
	Name          string                             `json:"name"`
	Image         string                             `json:"image"`
	ContainerPort uint64                             `json:"container_port"`
	Replicas      uint64                             `json:"replicas"`
	Environments  []CreateProjectEnvironmentResponse `json:"environments"`
	Services      []CreateProjectServiceResponse     `json:"services"`
}

type CreateProjectEnvironmentResponse struct {
	ID    uint64 `json:"id"`
	Name  string `json:"name"`
	Value string `json:"value"`
}

type CreateProjectServiceResponse struct {
	ID          uint64                            `json:"id"`
	CreatedAt   time.Time                         `json:"created_at"`
	UpdatedAt   time.Time                         `json:"updated_at"`
	Name        string                            `json:"name"`
	Port        string                            `json:"port"`
	TargetPort  string                            `json:"target_port"`
	Type        string                            `json:"type"`
	ClusterIP   string                            `json:"cluster_ip"`
	ExternalIP  string                            `json:"external_ip"`
	IngressSpec *CreateProjectIngressSpecResponse `json:"ingress_spec"`
}

type CreateProjectIngressSpecResponse struct {
	ID   uint64 `json:"id"`
	Host string `json:"host"`
	Path string `json:"path"`
	Port uint64 `json:"port"`
}

type CreateProjectIngressResponse struct {
	ID    uint64 `json:"id"`
	Name  string `json:"name"`
	Class string `json:"class"`
}

type UpdateProjectIsActiveRequest struct {
	IsActive bool `json:"is_active"`
}

// ProjectListItemResponse represents a project in list view - extends NamespaceListItemResponse
type ProjectListItemResponse struct {
	*NamespaceDetailResponse
	DeploymentCount int `json:"deployment_count"`
	ServiceCount    int `json:"service_count"`
	IngressCount    int `json:"ingress_count"`
}

// CreateProjectResponse represents the response after creating a project
type CreateProjectResponse struct {
	*NamespaceDetailResponse
}

// UpdateProjectResponse represents the response after updating a project
type UpdateProjectResponse struct {
	*NamespaceDetailResponse
}

// ProjectDetailResponse represents a single project detail view
type ProjectDetailResponse struct {
	*NamespaceDetailResponse
}

// Convert response functions
func ToProjectListItemDTO(d *domain.Namespace) *ProjectListItemResponse {
	namespaceResponse := ToNamespaceDetailDTO(d)

	return &ProjectListItemResponse{
		NamespaceDetailResponse: namespaceResponse,
		DeploymentCount:         len(d.Deployments),
		ServiceCount:            len(d.Services),
		IngressCount:            len(d.Ingress),
	}
}

func ToCreateProjectResponseDTO(d *domain.Namespace) *CreateProjectResponse {
	return &CreateProjectResponse{
		NamespaceDetailResponse: ToNamespaceDetailDTO(d),
	}
}

func ToUpdateProjectResponseDTO(d *domain.Namespace) *UpdateProjectResponse {
	return &UpdateProjectResponse{
		NamespaceDetailResponse: ToNamespaceDetailDTO(d),
	}
}

func ToProjectDetailDTO(d *domain.Namespace) *ProjectDetailResponse {
	return &ProjectDetailResponse{
		NamespaceDetailResponse: ToNamespaceDetailDTO(d),
	}
}
