package dto

import (
	"ops-api/internal/core/domain"
	"time"
)

type CreateJobRequest struct {
	Name        string           `json:"name" validate:"required,min=2,max=100"`
	Description string           `json:"description" validate:"max=500"`
	JobStatusID uint64           `json:"job_status_id" validate:"required"`
	EventID     *uint64          `json:"event_id,omitempty"`
	Event       domain.JobEvent  `json:"event"`
	Action      domain.JobAction `json:"action"`
}

type UpdateJobRequest struct {
	Name        string           `json:"name" validate:"required,min=2,max=100"`
	Description string           `json:"description" validate:"max=500"`
	JobStatusID uint64           `json:"job_status_id" validate:"required"`
	EventID     *uint64          `json:"event_id,omitempty"`
	Event       domain.JobEvent  `json:"event"`
	Action      domain.JobAction `json:"action"`
}

type JobListItemResponse struct {
	ID          uint64                     `json:"id"`
	CreatedAt   time.Time                  `json:"created_at"`
	UpdatedAt   time.Time                  `json:"updated_at"`
	Name        string                     `json:"name"`
	Description string                     `json:"description"`
	EventID     *uint64                    `json:"event_id,omitempty"`
	Event       *domain.JobEvent           `json:"event"`
	Action      *domain.JobAction          `json:"action"`
	JobStatus   *JobStatusRelationResponse `json:"job_status,omitempty"`
	User        *UserRelationResponse      `json:"user,omitempty"`
	JobLogs     []JobLogRelationResponse   `json:"job_logs,omitempty"`
}

type JobDetailResponse struct {
	ID          uint64                     `json:"id"`
	CreatedAt   time.Time                  `json:"created_at"`
	UpdatedAt   time.Time                  `json:"updated_at"`
	Name        string                     `json:"name"`
	Description string                     `json:"description"`
	EventID     *uint64                    `json:"event_id,omitempty"`
	Event       *domain.JobEvent           `json:"event"`
	Action      *domain.JobAction          `json:"action"`
	JobStatus   *JobStatusRelationResponse `json:"job_status,omitempty"`
	User        *UserRelationResponse      `json:"user,omitempty"`
	JobLogs     []JobLogRelationResponse   `json:"job_logs,omitempty"`
}

type JobRelationResponse struct {
	ID          uint64            `json:"id"`
	Name        string            `json:"name"`
	Description string            `json:"description"`
	EventID     *uint64           `json:"event_id,omitempty"`
	Event       *domain.JobEvent  `json:"event"`
	Action      *domain.JobAction `json:"action"`
}

// Convert response

func ToJobListItemDTO(d *domain.Job) *JobListItemResponse {
	// Convert job logs slice
	jobLogs := make([]JobLogRelationResponse, 0, len(d.JobLogs))
	for _, jobLog := range d.JobLogs {
		if jobLog != nil {
			jobLogs = append(jobLogs, *ToJobLogRelationDTO(jobLog))
		}
	}
	return &JobListItemResponse{
		ID:          d.ID,
		CreatedAt:   d.CreatedAt,
		UpdatedAt:   d.UpdatedAt,
		Name:        d.Name,
		Description: d.Description,
		EventID:     d.EventID,
		Event:       d.Event,
		Action:      d.Action,
		JobStatus:   ToJobStatusRelationDTO(d.JobStatus),
		User:        ToUserRelationDTO(d.User),
		JobLogs:     jobLogs,
	}
}

func ToJobDetailDTO(d *domain.Job) *JobDetailResponse {
	// Convert job logs slice
	jobLogs := make([]JobLogRelationResponse, 0, len(d.JobLogs))
	for _, jobLog := range d.JobLogs {
		if jobLog != nil {
			jobLogs = append(jobLogs, *ToJobLogRelationDTO(jobLog))
		}
	}

	return &JobDetailResponse{
		ID:          d.ID,
		CreatedAt:   d.CreatedAt,
		UpdatedAt:   d.UpdatedAt,
		Name:        d.Name,
		Description: d.Description,
		EventID:     d.EventID,
		Event:       d.Event,
		Action:      d.Action,
		JobStatus:   ToJobStatusRelationDTO(d.JobStatus),
		User:        ToUserRelationDTO(d.User),
		JobLogs:     jobLogs,
	}
}

func ToJobRelationDTO(d *domain.Job) *JobRelationResponse {
	return &JobRelationResponse{
		ID:          d.ID,
		Name:        d.Name,
		Description: d.Description,
		EventID:     d.EventID,
		Event:       d.Event,
		Action:      d.Action,
	}
}
