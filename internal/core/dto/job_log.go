package dto

import (
	"ops-api/internal/core/domain"
	"time"
)

type CreateJobLogRequest struct {
	Name        string `json:"name" validate:"required,min=2,max=100"`
	Description string `json:"description" validate:"max=500"`
	JobID       uint64 `json:"job_id" validate:"required"`
}

type UpdateJobLogRequest struct {
	Name        string `json:"name" validate:"required,min=2,max=100"`
	Description string `json:"description" validate:"max=500"`
	JobID       uint64 `json:"job_id" validate:"required"`
}

type JobLogListItemResponse struct {
	ID          uint64               `json:"id"`
	CreatedAt   time.Time            `json:"created_at"`
	UpdatedAt   time.Time            `json:"updated_at"`
	Name        string               `json:"name"`
	Description string               `json:"description"`
	Job         *JobRelationResponse `json:"job,omitempty"`
}

type JobLogDetailResponse struct {
	ID          uint64               `json:"id"`
	CreatedAt   time.Time            `json:"created_at"`
	UpdatedAt   time.Time            `json:"updated_at"`
	Name        string               `json:"name"`
	Description string               `json:"description"`
	Job         *JobRelationResponse `json:"job,omitempty"`
}

type JobLogRelationResponse struct {
	ID          uint64 `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
}

// Convert response

func ToJobLogListItemDTO(d *domain.JobLog) *JobLogListItemResponse {
	return &JobLogListItemResponse{
		ID:          d.ID,
		CreatedAt:   d.CreatedAt,
		UpdatedAt:   d.UpdatedAt,
		Name:        d.Name,
		Description: d.Description,
		Job:         ToJobRelationDTO(d.Job),
	}
}

func ToJobLogDetailDTO(d *domain.JobLog) *JobLogDetailResponse {
	return &JobLogDetailResponse{
		ID:          d.ID,
		CreatedAt:   d.CreatedAt,
		UpdatedAt:   d.UpdatedAt,
		Name:        d.Name,
		Description: d.Description,
		Job:         ToJobRelationDTO(d.Job),
	}
}

func ToJobLogRelationDTO(d *domain.JobLog) *JobLogRelationResponse {
	return &JobLogRelationResponse{
		ID:          d.ID,
		Name:        d.Name,
		Description: d.Description,
	}
}
