package dto

import (
	"ops-api/internal/core/domain"
	"time"
)

type CreateOrderNamespaceRequest struct {
	OrderID     uint64 `json:"order_id" validate:"required"`
	NamespaceID uint64 `json:"namespace_id" validate:"required"`
}

type UpdateOrderNamespaceRequest struct {
	OrderID     uint64 `json:"order_id" validate:"required"`
	NamespaceID uint64 `json:"namespace_id" validate:"required"`
}

type OrderNamespaceListItemResponse struct {
	ID          uint64                     `json:"id"`
	CreatedAt   time.Time                  `json:"created_at"`
	UpdatedAt   time.Time                  `json:"updated_at"`
	OrderID     uint64                     `json:"order_id"`
	NamespaceID uint64                     `json:"namespace_id"`
	Order       *OrderRelationResponse     `json:"order,omitempty"`
	Namespace   *NamespaceRelationResponse `json:"namespace,omitempty"`
}

type OrderNamespaceDetailResponse struct {
	ID          uint64                     `json:"id"`
	CreatedAt   time.Time                  `json:"created_at"`
	UpdatedAt   time.Time                  `json:"updated_at"`
	OrderID     uint64                     `json:"order_id"`
	NamespaceID uint64                     `json:"namespace_id"`
	Order       *OrderRelationResponse     `json:"order,omitempty"`
	Namespace   *NamespaceRelationResponse `json:"namespace,omitempty"`
}

type OrderNamespaceRelationResponse struct {
	ID          uint64 `json:"id"`
	OrderID     uint64 `json:"order_id"`
	NamespaceID uint64 `json:"namespace_id"`
}

// Convert response

func ToOrderNamespaceListItemDTO(d *domain.OrderNamespace) *OrderNamespaceListItemResponse {
	if d == nil {
		return nil
	}

	return &OrderNamespaceListItemResponse{
		ID:          d.ID,
		CreatedAt:   d.CreatedAt,
		UpdatedAt:   d.UpdatedAt,
		OrderID:     d.OrderID,
		NamespaceID: d.NamespaceID,
		Order:       ToOrderRelationDTO(d.Order),
		Namespace:   ToNamespaceRelationDTO(d.Namespace),
	}
}

func ToOrderNamespaceDetailDTO(d *domain.OrderNamespace) *OrderNamespaceDetailResponse {
	if d == nil {
		return nil
	}

	return &OrderNamespaceDetailResponse{
		ID:          d.ID,
		CreatedAt:   d.CreatedAt,
		UpdatedAt:   d.UpdatedAt,
		OrderID:     d.OrderID,
		NamespaceID: d.NamespaceID,
		Order:       ToOrderRelationDTO(d.Order),
		Namespace:   ToNamespaceRelationDTO(d.Namespace),
	}
}

func ToOrderNamespaceRelationDTO(d *domain.OrderNamespace) *OrderNamespaceRelationResponse {
	if d == nil {
		return nil
	}
	return &OrderNamespaceRelationResponse{
		ID:          d.ID,
		OrderID:     d.OrderID,
		NamespaceID: d.NamespaceID,
	}
}
