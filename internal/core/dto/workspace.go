package dto

import (
	"ops-api/internal/core/domain"
	"time"
)

type CreateWorkspaceRequest struct {
	Name        string `json:"name" validate:"required,min=2,max=100"`
	Description string `json:"description"`
	UserID      uint64 `json:"user_id" validate:"required"`
}

type UpdateWorkspaceRequest struct {
	Name        string `json:"name" validate:"required,min=2,max=100"`
	Description string `json:"description"`
}

type WorkspaceListItemResponse struct {
	ID          uint64                `json:"id"`
	CreatedAt   time.Time             `json:"created_at"`
	UpdatedAt   time.Time             `json:"updated_at"`
	Name        string                `json:"name"`
	Description string                `json:"description"`
	User        *UserRelationResponse `json:"user,omitempty"`
}

type WorkspaceDetailResponse struct {
	ID          uint64                    `json:"id"`
	CreatedAt   time.Time                 `json:"created_at"`
	UpdatedAt   time.Time                 `json:"updated_at"`
	Name        string                    `json:"name"`
	Description string                    `json:"description"`
	User        *UserRelationResponse     `json:"user,omitempty"`
	Clusters    []ClusterRelationResponse `json:"clusters,omitempty"`
}

type WorkspaceRelationResponse struct {
	ID          uint64 `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
}

// Convert response

func ToWorkspaceListItemDTO(d *domain.Workspace) *WorkspaceListItemResponse {
	var user *UserRelationResponse
	if d.User != nil {
		user = ToUserRelationDTO(d.User)
	}

	return &WorkspaceListItemResponse{
		ID:          d.ID,
		CreatedAt:   d.CreatedAt,
		UpdatedAt:   d.UpdatedAt,
		Name:        d.Name,
		Description: d.Description,
		User:        user,
	}
}

func ToWorkspaceDetailDTO(d *domain.Workspace) *WorkspaceDetailResponse {
	var user *UserRelationResponse
	if d.User != nil {
		user = ToUserRelationDTO(d.User)
	}

	var clusters []ClusterRelationResponse
	for _, cluster := range d.Clusters {
		clusters = append(clusters, *ToClusterRelationDTO(&cluster))
	}

	return &WorkspaceDetailResponse{
		ID:          d.ID,
		CreatedAt:   d.CreatedAt,
		UpdatedAt:   d.UpdatedAt,
		Name:        d.Name,
		Description: d.Description,
		User:        user,
		Clusters:    clusters,
	}
}

func ToWorkspaceRelationDTO(d *domain.Workspace) *WorkspaceRelationResponse {
	return &WorkspaceRelationResponse{
		ID:          d.ID,
		Name:        d.Name,
		Description: d.Description,
	}
}
