package dto

import (
	"ops-api/internal/core/domain"
	"time"
)

type CreateDeploymentRequest struct {
	Name          string `json:"name" validate:"required,min=2,max=50"`
	Image         string `json:"image" validate:"required"`
	ContainerPort uint64 `json:"container_port" validate:"required,min=1,max=65535"`
	Replicas      uint64 `json:"replicas" validate:"required,min=1,max=10"`
	NamespaceID   uint64 `json:"namespace_id" validate:"required"`
}

type UpdateDeploymentRequest struct {
	Name          string `json:"name" validate:"required,min=2,max=50"`
	Image         string `json:"image" validate:"required"`
	ContainerPort uint64 `json:"container_port" validate:"required,min=1,max=65535"`
	Replicas      uint64 `json:"replicas" validate:"required,min=1,max=10"`
	NamespaceID   uint64 `json:"namespace_id" validate:"required"`
	StatusID      uint64 `json:"status_id" validate:"required"`
}

type UpdateDeploymentStatusRequest struct {
	StatusID uint64 `json:"status_id" validate:"required"`
}

type DeploymentListItemResponse struct {
	ID            uint64                        `json:"id"`
	CreatedAt     time.Time                     `json:"created_at"`
	UpdatedAt     time.Time                     `json:"updated_at"`
	Name          string                        `json:"name"`
	Image         string                        `json:"image"`
	ContainerPort uint64                        `json:"container_port"`
	Replicas      uint64                        `json:"replicas"`
	LastDeployed  *time.Time                    `json:"last_deployed,omitempty"`
	Namespace     *NamespaceRelationResponse    `json:"namespace,omitempty"`
	Status        *ServerStatusRelationResponse `json:"status,omitempty"`
}

type DeploymentDetailResponse struct {
	ID            uint64                        `json:"id"`
	CreatedAt     time.Time                     `json:"created_at"`
	UpdatedAt     time.Time                     `json:"updated_at"`
	Name          string                        `json:"name"`
	Image         string                        `json:"image"`
	ContainerPort uint64                        `json:"container_port"`
	Replicas      uint64                        `json:"replicas"`
	LastDeployed  *time.Time                    `json:"last_deployed,omitempty"`
	Namespace     *NamespaceRelationResponse    `json:"namespace,omitempty"`
	Status        *ServerStatusRelationResponse `json:"status,omitempty"`
	Environments  []EnvironmentRelationResponse `json:"environments,omitempty"`
}

type DeploymentRelationResponse struct {
	ID            uint64                        `json:"id"`
	Name          string                        `json:"name"`
	Image         string                        `json:"image"`
	ContainerPort uint64                        `json:"container_port"`
	Replicas      uint64                        `json:"replicas"`
	LastDeployed  *time.Time                    `json:"last_deployed,omitempty"`
	Status        *ServerStatusRelationResponse `json:"status,omitempty"`
}

type DeploymentWithEnvironmentsResponse struct {
	ID            uint64                        `json:"id"`
	Name          string                        `json:"name"`
	Image         string                        `json:"image"`
	ContainerPort uint64                        `json:"container_port"`
	Replicas      uint64                        `json:"replicas"`
	LastDeployed  *time.Time                    `json:"last_deployed,omitempty"`
	Status        *ServerStatusRelationResponse `json:"status,omitempty"`
	Environments  []EnvironmentRelationResponse `json:"environments,omitempty"`
}

// Convert response

func ToDeploymentListItemDTO(d *domain.Deployment) *DeploymentListItemResponse {
	return &DeploymentListItemResponse{
		ID:            d.ID,
		CreatedAt:     d.CreatedAt,
		UpdatedAt:     d.UpdatedAt,
		Name:          d.Name,
		Image:         d.Image,
		ContainerPort: d.ContainerPort,
		Replicas:      d.Replicas,
		LastDeployed:  d.LastDeployedAt,
		Namespace:     ToNamespaceRelationDTO(d.Namespace),
		Status:        ToServerStatusRelationDTO(d.Status),
	}
}

func ToDeploymentDetailDTO(d *domain.Deployment) *DeploymentDetailResponse {
	resp := &DeploymentDetailResponse{
		ID:            d.ID,
		CreatedAt:     d.CreatedAt,
		UpdatedAt:     d.UpdatedAt,
		Name:          d.Name,
		Image:         d.Image,
		ContainerPort: d.ContainerPort,
		Replicas:      d.Replicas,
		LastDeployed:  d.LastDeployedAt,
		Namespace:     ToNamespaceRelationDTO(d.Namespace),
		Status:        ToServerStatusRelationDTO(d.Status),
	}

	// Convert environments if they exist
	if d.Environments != nil {
		resp.Environments = make([]EnvironmentRelationResponse, len(d.Environments))
		for i, env := range d.Environments {
			resp.Environments[i] = *ToEnvironmentRelationDTO(env)
		}
	}

	return resp
}

func ToDeploymentRelationDTO(d *domain.Deployment) *DeploymentRelationResponse {
	if d == nil {
		return nil
	}
	return &DeploymentRelationResponse{
		ID:            d.ID,
		Name:          d.Name,
		Image:         d.Image,
		ContainerPort: d.ContainerPort,
		Replicas:      d.Replicas,
		LastDeployed:  d.LastDeployedAt,
		Status:        ToServerStatusRelationDTO(d.Status),
	}
}
