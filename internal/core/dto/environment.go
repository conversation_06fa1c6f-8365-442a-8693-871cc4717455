package dto

import (
	"ops-api/internal/core/domain"
	"time"
)

type CreateEnvironmentRequest struct {
	Name         string `json:"name" validate:"required,min=1,max=100"`
	Value        string `json:"value" validate:"required"`
	DeploymentID uint64 `json:"deployment_id" validate:"required"`
}

type UpdateEnvironmentRequest struct {
	Name         string `json:"name" validate:"required,min=1,max=100"`
	Value        string `json:"value" validate:"required"`
	DeploymentID uint64 `json:"deployment_id" validate:"required"`
}

type EnvironmentListItemResponse struct {
	ID         uint64                      `json:"id"`
	CreatedAt  time.Time                   `json:"created_at"`
	UpdatedAt  time.Time                   `json:"updated_at"`
	Name       string                      `json:"name"`
	Value      string                      `json:"value"`
	Deployment *DeploymentRelationResponse `json:"deployment,omitempty"`
}

type EnvironmentDetailResponse struct {
	ID         uint64                      `json:"id"`
	CreatedAt  time.Time                   `json:"created_at"`
	UpdatedAt  time.Time                   `json:"updated_at"`
	Name       string                      `json:"name"`
	Value      string                      `json:"value"`
	Deployment *DeploymentRelationResponse `json:"deployment,omitempty"`
}

type EnvironmentRelationResponse struct {
	ID    uint64 `json:"id"`
	Name  string `json:"name"`
	Value string `json:"value"`
}

// Convert response functions

func ToEnvironmentListItemDTO(e *domain.Environment) *EnvironmentListItemResponse {
	resp := &EnvironmentListItemResponse{
		ID:        e.ID,
		CreatedAt: e.CreatedAt,
		UpdatedAt: e.UpdatedAt,
		Name:      e.Name,
		Value:     e.Value,
	}

	if e.Deployment != nil {
		resp.Deployment = ToDeploymentRelationDTO(e.Deployment)
	}

	return resp
}

func ToEnvironmentDetailDTO(e *domain.Environment) *EnvironmentDetailResponse {
	resp := &EnvironmentDetailResponse{
		ID:        e.ID,
		CreatedAt: e.CreatedAt,
		UpdatedAt: e.UpdatedAt,
		Name:      e.Name,
		Value:     e.Value,
	}

	if e.Deployment != nil {
		resp.Deployment = ToDeploymentRelationDTO(e.Deployment)
	}

	return resp
}

func ToEnvironmentRelationDTO(e *domain.Environment) *EnvironmentRelationResponse {
	return &EnvironmentRelationResponse{
		ID:    e.ID,
		Name:  e.Name,
		Value: e.Value,
	}
}
