package dto

import (
	"encoding/json"
	"fmt"
)

// NamecheapCheckRequest represents the request structure for domain checking
type NamecheapCheckRequest struct {
	Domains []string `json:"domains" validate:"required,min=1,dive,required,min=1"`
}

// NamecheapPricingRequest represents the request structure for pricing queries
type NamecheapPricingRequest struct {
	ProductName string `json:"product_name" validate:"required,min=1"`
}

// NamecheapDomainCheckResult represents a single domain check result from Namecheap API
type NamecheapDomainCheckResult struct {
	Domain                   string `json:"Domain"`
	Available                string `json:"Available"`
	ErrorNo                  string `json:"ErrorNo"`
	Description              string `json:"Description"`
	IsPremiumName            string `json:"IsPremiumName"`
	PremiumRegistrationPrice string `json:"PremiumRegistrationPrice"`
	PremiumRenewalPrice      string `json:"PremiumRenewalPrice"`
	PremiumRestorePrice      string `json:"PremiumRestorePrice"`
	PremiumTransferPrice     string `json:"PremiumTransferPrice"`
	IcannFee                 string `json:"IcannFee"`
	EapFee                   string `json:"EapFee"`
}

// NamecheapDomainCheckResultWithPricing represents a domain check result enhanced with pricing information
type NamecheapDomainCheckResultWithPricing struct {
	Domain                   string           `json:"Domain"`
	Available                string           `json:"Available"`
	ErrorNo                  string           `json:"ErrorNo"`
	Description              string           `json:"Description"`
	IsPremiumName            string           `json:"IsPremiumName"`
	PremiumRegistrationPrice string           `json:"PremiumRegistrationPrice"`
	PremiumRenewalPrice      string           `json:"PremiumRenewalPrice"`
	PremiumRestorePrice      string           `json:"PremiumRestorePrice"`
	PremiumTransferPrice     string           `json:"PremiumTransferPrice"`
	IcannFee                 string           `json:"IcannFee"`
	EapFee                   string           `json:"EapFee"`
	Price                    []NamecheapPrice `json:"Price,omitempty"`
}

// NamecheapPrice represents a single price entry for a domain product
type NamecheapPrice struct {
	Duration                  string `json:"Duration"`
	DurationType              string `json:"DurationType"`
	Price                     string `json:"Price"`
	PricingType               string `json:"PricingType"`
	AdditionalCost            string `json:"AdditionalCost"`
	RegularPrice              string `json:"RegularPrice"`
	RegularPriceType          string `json:"RegularPriceType"`
	RegularAdditionalCost     string `json:"RegularAdditionalCost"`
	RegularAdditionalCostType string `json:"RegularAdditionalCostType"`
	YourPrice                 string `json:"YourPrice"`
	YourPriceType             string `json:"YourPriceType"`
	YourAdditonalCost         string `json:"YourAdditonalCost"`
	YourAdditonalCostType     string `json:"YourAdditonalCostType"`
	PromotionPrice            string `json:"PromotionPrice"`
	Currency                  string `json:"Currency"`
}

// NamecheapProduct represents a product with its pricing information
type NamecheapProduct struct {
	Name  string           `json:"Name"`
	Price []NamecheapPrice `json:"Price"`
}

// NamecheapProductCategory represents a product category containing products
type NamecheapProductCategory struct {
	Name    string           `json:"Name"`
	Product NamecheapProduct `json:"Product"`
}

// NamecheapProductType represents a product type containing categories
type NamecheapProductType struct {
	Name            string                   `json:"Name"`
	ProductCategory NamecheapProductCategory `json:"ProductCategory"`
}

// NamecheapUserGetPricingResult represents the pricing result structure
type NamecheapUserGetPricingResult struct {
	ProductType NamecheapProductType `json:"ProductType"`
}

// NamecheapCommandResponse represents the CommandResponse section of the API response
type NamecheapCommandResponse struct {
	Type                 string                        `json:"Type"`
	DomainCheckResult    []NamecheapDomainCheckResult  `json:"-"` // Custom unmarshaling
	UserGetPricingResult NamecheapUserGetPricingResult `json:"UserGetPricingResult,omitempty"`
}

// UnmarshalJSON implements custom JSON unmarshaling for NamecheapCommandResponse
// to handle the case where DomainCheckResult can be either a single object or an array
func (n *NamecheapCommandResponse) UnmarshalJSON(data []byte) error {
	// Create a temporary struct with the same fields but without custom unmarshaling
	type Alias NamecheapCommandResponse
	aux := &struct {
		DomainCheckResult json.RawMessage `json:"DomainCheckResult,omitempty"`
		*Alias
	}{
		Alias: (*Alias)(n),
	}

	if err := json.Unmarshal(data, &aux); err != nil {
		return err
	}

	// Handle DomainCheckResult field
	if len(aux.DomainCheckResult) > 0 {
		// Try to unmarshal as array first
		var resultArray []NamecheapDomainCheckResult
		if err := json.Unmarshal(aux.DomainCheckResult, &resultArray); err == nil {
			n.DomainCheckResult = resultArray
		} else {
			// If array unmarshaling fails, try as single object
			var singleResult NamecheapDomainCheckResult
			if err := json.Unmarshal(aux.DomainCheckResult, &singleResult); err != nil {
				return fmt.Errorf("failed to unmarshal DomainCheckResult as both array and single object: %v", err)
			}
			n.DomainCheckResult = []NamecheapDomainCheckResult{singleResult}
		}
	}

	return nil
}

// NamecheapApiResponse represents the ApiResponse section of the API response
type NamecheapApiResponse struct {
	Status            string                   `json:"Status"`
	Xmlns             string                   `json:"xmlns"`
	Errors            string                   `json:"Errors"`
	Warnings          string                   `json:"Warnings"`
	RequestedCommand  string                   `json:"RequestedCommand"`
	CommandResponse   NamecheapCommandResponse `json:"CommandResponse"`
	Server            string                   `json:"Server"`
	GMTTimeDifference string                   `json:"GMTTimeDifference"`
	ExecutionTime     string                   `json:"ExecutionTime"`
}

// NamecheapProxyData represents the data section of the proxy response
type NamecheapProxyData struct {
	ApiResponse NamecheapApiResponse `json:"ApiResponse"`
}

// NamecheapProxyResponse represents the full response from the Namecheap proxy
type NamecheapProxyResponse struct {
	Success     bool               `json:"success"`
	Data        NamecheapProxyData `json:"data"`
	OriginalXml string             `json:"originalXml"`
	ConvertedAt string             `json:"convertedAt"`
}

// NamecheapCheckResponse represents the response structure for domain checking
type NamecheapCheckResponse struct {
	CommandResponse NamecheapCommandResponse `json:"command_response"`
}

// NamecheapCheckResponseWithPricing represents the enhanced response structure for domain checking with pricing
type NamecheapCheckResponseWithPricing struct {
	DomainCheckResult []NamecheapDomainCheckResultWithPricing `json:"DomainCheckResult"`
}

// NamecheapPricingResponse represents the response structure for pricing queries
type NamecheapPricingResponse struct {
	CommandResponse NamecheapCommandResponse `json:"command_response"`
}

// ConvertNamecheapProxyResponse converts the raw Namecheap proxy response to return CommandResponse directly
func ConvertNamecheapProxyResponse(proxyResponse *NamecheapProxyResponse) *NamecheapCheckResponse {
	if proxyResponse.Success && proxyResponse.Data.ApiResponse.Status == "OK" {
		return &NamecheapCheckResponse{
			CommandResponse: proxyResponse.Data.ApiResponse.CommandResponse,
		}
	}

	// Return empty CommandResponse if not successful
	return &NamecheapCheckResponse{
		CommandResponse: NamecheapCommandResponse{},
	}
}

// ConvertNamecheapPricingProxyResponse converts the raw Namecheap proxy response to return CommandResponse directly for pricing
func ConvertNamecheapPricingProxyResponse(proxyResponse *NamecheapProxyResponse) *NamecheapPricingResponse {
	if proxyResponse.Success && proxyResponse.Data.ApiResponse.Status == "OK" {
		return &NamecheapPricingResponse{
			CommandResponse: proxyResponse.Data.ApiResponse.CommandResponse,
		}
	}

	// Return empty CommandResponse if not successful
	return &NamecheapPricingResponse{
		CommandResponse: NamecheapCommandResponse{},
	}
}
