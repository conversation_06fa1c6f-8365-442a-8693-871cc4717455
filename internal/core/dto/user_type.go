package dto

import (
	"ops-api/internal/core/domain"
	"time"
)

type CreateUserTypeRequest struct {
	Name        string `json:"name" validate:"required,min=2,max=50"`
	Description string `json:"description" validate:"max=255"`
	IsActive    *bool  `json:"is_active,omitempty"`
	IsAdmin     *bool  `json:"is_admin,omitempty"`
	IsMember    *bool  `json:"is_member,omitempty"`
	IsSale      *bool  `json:"is_sale,omitempty"`
}

type UpdateUserTypeRequest struct {
	Name        string `json:"name" validate:"required,min=2,max=50"`
	Description string `json:"description" validate:"max=255"`
	IsActive    *bool  `json:"is_active,omitempty"`
	IsAdmin     *bool  `json:"is_admin,omitempty"`
	IsMember    *bool  `json:"is_member,omitempty"`
	IsSale      *bool  `json:"is_sale,omitempty"`
}

type UserTypeListItemResponse struct {
	ID          uint64    `json:"id"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	IsActive    bool      `json:"is_active"`
	IsAdmin     bool      `json:"is_admin"`
	IsMember    bool      `json:"is_member"`
	IsSale      bool      `json:"is_sale"`
}

type UserTypeDetailResponse struct {
	ID          uint64    `json:"id"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	IsActive    bool      `json:"is_active"`
	IsAdmin     bool      `json:"is_admin"`
	IsMember    bool      `json:"is_member"`
	IsSale      bool      `json:"is_sale"`
}

type UserTypeRelationResponse struct {
	ID          uint64 `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
	IsActive    bool   `json:"is_active"`
	IsAdmin     bool   `json:"is_admin"`
	IsMember    bool   `json:"is_member"`
	IsSale      bool   `json:"is_sale"`
}

// Convert response

func ToUserTypeListItemDTO(d *domain.UserType) *UserTypeListItemResponse {
	if d == nil {
		return nil
	}
	return &UserTypeListItemResponse{
		ID:          d.ID,
		CreatedAt:   d.CreatedAt,
		UpdatedAt:   d.UpdatedAt,
		Name:        d.Name,
		Description: d.Description,
		IsActive:    d.IsActive,
		IsAdmin:     d.IsAdmin,
		IsMember:    d.IsMember,
		IsSale:      d.IsSale,
	}
}

func ToUserTypeDetailDTO(d *domain.UserType) *UserTypeDetailResponse {
	if d == nil {
		return nil
	}
	return &UserTypeDetailResponse{
		ID:          d.ID,
		CreatedAt:   d.CreatedAt,
		UpdatedAt:   d.UpdatedAt,
		Name:        d.Name,
		Description: d.Description,
		IsActive:    d.IsActive,
		IsAdmin:     d.IsAdmin,
		IsMember:    d.IsMember,
		IsSale:      d.IsSale,
	}
}

func ToUserTypeRelationDTO(d *domain.UserType) *UserTypeRelationResponse {
	if d == nil {
		return nil
	}
	return &UserTypeRelationResponse{
		ID:          d.ID,
		Name:        d.Name,
		Description: d.Description,
		IsActive:    d.IsActive,
		IsAdmin:     d.IsAdmin,
		IsMember:    d.IsMember,
		IsSale:      d.IsSale,
	}
}
