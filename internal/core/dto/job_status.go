package dto

import (
	"ops-api/internal/core/domain"
	"time"
)

type CreateJobStatusRequest struct {
	Name string `json:"name" validate:"required,min=2,max=50"`
}

type UpdateJobStatusRequest struct {
	Name string `json:"name" validate:"required,min=2,max=50"`
}

type JobStatusListItemResponse struct {
	ID        uint64    `json:"id"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	Name      string    `json:"name"`
}

type JobStatusDetailResponse struct {
	ID        uint64    `json:"id"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	Name      string    `json:"name"`
}

type JobStatusRelationResponse struct {
	ID   uint64 `json:"id"`
	Name string `json:"name"`
}

// Convert response

func ToJobStatusListItemDTO(d *domain.JobStatus) *JobStatusListItemResponse {
	return &JobStatusListItemResponse{
		ID:        d.ID,
		CreatedAt: d.<PERSON>t,
		UpdatedAt: d.UpdatedAt,
		Name:      d.Name,
	}
}

func ToJobStatusDetailDTO(d *domain.JobStatus) *JobStatusDetailResponse {
	return &JobStatusDetailResponse{
		ID:        d.ID,
		CreatedAt: d.CreatedAt,
		UpdatedAt: d.UpdatedAt,
		Name:      d.Name,
	}
}

func ToJobStatusRelationDTO(d *domain.JobStatus) *JobStatusRelationResponse {
	return &JobStatusRelationResponse{
		ID:   d.ID,
		Name: d.Name,
	}
}
