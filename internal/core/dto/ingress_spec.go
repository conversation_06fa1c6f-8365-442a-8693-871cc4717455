package dto

import (
	"ops-api/internal/core/domain"
	"time"
)

type CreateIngressSpecRequest struct {
	Host      string `json:"host" validate:"required"`
	Path      string `json:"path" validate:"required"`
	Port      uint64 `json:"port" validate:"required,min=1,max=65535"`
	ServiceID uint64 `json:"service_id" validate:"required"`
	IngressID uint64 `json:"ingress_id" validate:"required"`
}

type UpdateIngressSpecRequest struct {
	Host      string `json:"host" validate:"required"`
	Path      string `json:"path" validate:"required"`
	Port      uint64 `json:"port" validate:"required,min=1,max=65535"`
	ServiceID uint64 `json:"service_id" validate:"required"`
	IngressID uint64 `json:"ingress_id" validate:"required"`
}

type IngressSpecListItemResponse struct {
	ID        uint64                   `json:"id"`
	CreatedAt time.Time                `json:"created_at"`
	UpdatedAt time.Time                `json:"updated_at"`
	Host      string                   `json:"host"`
	Path      string                   `json:"path"`
	Port      uint64                   `json:"port"`
	Service   *ServiceRelationResponse `json:"service,omitempty"`
	Ingress   *IngressRelationResponse `json:"ingress,omitempty"`
}

type IngressSpecDetailResponse struct {
	ID        uint64                   `json:"id"`
	CreatedAt time.Time                `json:"created_at"`
	UpdatedAt time.Time                `json:"updated_at"`
	Host      string                   `json:"host"`
	Path      string                   `json:"path"`
	Port      uint64                   `json:"port"`
	Service   *ServiceRelationResponse `json:"service,omitempty"`
	Ingress   *IngressRelationResponse `json:"ingress,omitempty"`
}

type IngressSpecRelationResponse struct {
	ID      uint64                   `json:"id"`
	Host    string                   `json:"host"`
	Path    string                   `json:"path"`
	Port    uint64                   `json:"port"`
	Service *ServiceRelationResponse `json:"service,omitempty"`
}

// Convert response functions

func ToIngressSpecListItemDTO(is *domain.IngressSpec) *IngressSpecListItemResponse {
	resp := &IngressSpecListItemResponse{
		ID:        is.ID,
		CreatedAt: is.CreatedAt,
		UpdatedAt: is.UpdatedAt,
		Host:      is.Host,
		Path:      is.Path,
		Port:      is.Port,
	}

	if is.Service != nil {
		resp.Service = ToServiceRelationDTO(is.Service)
	}

	if is.Ingress != nil {
		resp.Ingress = ToIngressRelationDTO(is.Ingress)
	}

	return resp
}

func ToIngressSpecDetailDTO(is *domain.IngressSpec) *IngressSpecDetailResponse {
	resp := &IngressSpecDetailResponse{
		ID:        is.ID,
		CreatedAt: is.CreatedAt,
		UpdatedAt: is.UpdatedAt,
		Host:      is.Host,
		Path:      is.Path,
		Port:      is.Port,
	}

	if is.Service != nil {
		resp.Service = ToServiceRelationDTO(is.Service)
	}

	if is.Ingress != nil {
		resp.Ingress = ToIngressRelationDTO(is.Ingress)
	}

	return resp
}

func ToIngressSpecRelationDTO(is *domain.IngressSpec) *IngressSpecRelationResponse {
	resp := &IngressSpecRelationResponse{
		ID:   is.ID,
		Host: is.Host,
		Path: is.Path,
		Port: is.Port,
	}
	if is.Service != nil {
		resp.Service = ToServiceRelationDTO(is.Service)
	}
	return resp
}
