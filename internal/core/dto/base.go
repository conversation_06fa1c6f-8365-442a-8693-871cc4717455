package dto

import (
	"math"
)

type PaginationRequest struct {
	Page       uint64 `json:"page"`
	Size       uint64 `json:"size"`
	SearchName string `json:"searchName"`
	Desc       bool   `json:"desc"`
}

func (p *PaginationRequest) GetOffset() int {
	return int((p.Page - 1) * p.<PERSON>ze)
}

type PaginationResponse struct {
	Content          interface{} `json:"content"`
	Pageable         Pageable    `json:"pageable"`
	Last             bool        `json:"last"`
	TotalElements    uint64      `json:"totalElements"`
	TotalPages       uint64      `json:"totalPages"`
	First            bool        `json:"first"`
	Size             uint64      `json:"size"`
	Number           uint64      `json:"number"`
	Sort             Sort        `json:"sort"`
	NumberOfElements uint64      `json:"numberOfElements"`
	Empty            bool        `json:"empty"`
}

type Sort struct {
	Empty    bool `json:"empty"`
	Unsorted bool `json:"unsorted"`
	Sorted   bool `json:"sorted"`
}

type Pageable struct {
	PageNumber uint64 `json:"pageNumber"`
	PageSize   uint64 `json:"pageSize"`
	Sort       Sort   `json:"sort"`
	Offset     uint64 `json:"offset"`
	Paged      bool   `json:"paged"`
	Unpaged    bool   `json:"unpaged"`
}

func NewPaginationResponse(content interface{}, page, size uint64, total uint64) PaginationResponse {
	totalPages := uint64(math.Ceil(float64(total) / float64(size)))

	// Ensure empty content is returned as [] instead of null in JSON
	if content == nil || total == 0 {
		content = []interface{}{}
	}

	return PaginationResponse{
		Content: content,
		Pageable: Pageable{
			PageNumber: page,
			PageSize:   size,
			Sort: Sort{
				Empty:    total == 0,
				Unsorted: true,
				Sorted:   false,
			},
			Offset:  0,
			Paged:   true,
			Unpaged: false,
		},
		Last:          page == totalPages,
		TotalElements: total,
		TotalPages:    totalPages,
		First:         page == 0,
		Size:          size,
		Number:        page,
		Sort: Sort{
			Empty:    true,
			Unsorted: true,
			Sorted:   false,
		},
		NumberOfElements: total,
		Empty:            total == 0,
	}
}
