package dto

import (
	"ops-api/internal/core/domain"
	"time"
)

type CreateClusterRequest struct {
	Name        string `json:"name" validate:"required,min=2,max=50"`
	Region      string `json:"region" validate:"required"`
	PoolName    string `json:"pool_name" validate:"required"`
	Size        string `json:"size" validate:"required"`
	NodeCount   uint64 `json:"node_count" validate:"required,min=1"`
	WorkspaceID uint64 `json:"workspace_id"`
}

type CreateSelfClusterRequest struct {
	WorkspaceID uint64 `json:"workspace_id"`
}

type UpdateClusterRequest struct {
	Name        string `json:"name" validate:"required,min=2,max=50"`
	Region      string `json:"region" validate:"required"`
	PoolName    string `json:"pool_name" validate:"required"`
	Size        string `json:"size" validate:"required"`
	NodeCount   uint64 `json:"node_count" validate:"required,min=1"`
	WorkspaceID uint64 `json:"workspace_id"`
	StatusID    uint64 `json:"status_id" validate:"required"`
}

type ClusterListItemResponse struct {
	ID            uint64                        `json:"id"`
	CreatedAt     time.Time                     `json:"created_at"`
	UpdatedAt     time.Time                     `json:"updated_at"`
	Name          string                        `json:"name"`
	Region        string                        `json:"region"`
	PoolName      string                        `json:"pool_name"`
	Size          string                        `json:"size"`
	NodeCount     uint64                        `json:"node_count"`
	LoadBalanceIP string                        `json:"load_balance_ip"`
	IsSelf        bool                          `json:"is_self"`
	Workspace     *WorkspaceRelationResponse    `json:"workspace,omitempty"`
	Status        *ServerStatusRelationResponse `json:"status,omitempty"`
}

type ClusterDetailResponse struct {
	ID            uint64                        `json:"id"`
	CreatedAt     time.Time                     `json:"created_at"`
	UpdatedAt     time.Time                     `json:"updated_at"`
	Name          string                        `json:"name"`
	Region        string                        `json:"region"`
	PoolName      string                        `json:"pool_name"`
	Size          string                        `json:"size"`
	NodeCount     uint64                        `json:"node_count"`
	LoadBalanceIP string                        `json:"load_balance_ip"`
	IsSelf        bool                          `json:"is_self"`
	Workspace     *WorkspaceRelationResponse    `json:"workspace,omitempty"`
	Status        *ServerStatusRelationResponse `json:"status,omitempty"`
	Namespaces    []NamespaceDetailResponse     `json:"namespaces,omitempty"`
}

type ClusterRelationResponse struct {
	ID            uint64                        `json:"id"`
	Name          string                        `json:"name"`
	Region        string                        `json:"region"`
	PoolName      string                        `json:"pool_name"`
	Size          string                        `json:"size"`
	NodeCount     uint64                        `json:"node_count"`
	LoadBalanceIP string                        `json:"load_balance_ip"`
	IsSelf        bool                          `json:"is_self"`
	Status        *ServerStatusRelationResponse `json:"status,omitempty"`
}

// Convert response

func ToClusterListItemDTO(d *domain.Cluster) *ClusterListItemResponse {
	var workspace *WorkspaceRelationResponse
	if d.Workspace != nil {
		workspace = ToWorkspaceRelationDTO(d.Workspace)
	}

	return &ClusterListItemResponse{
		ID:            d.ID,
		CreatedAt:     d.CreatedAt,
		UpdatedAt:     d.UpdatedAt,
		Name:          d.Name,
		Region:        d.Region,
		PoolName:      d.PoolName,
		Size:          d.Size,
		NodeCount:     d.NodeCount,
		LoadBalanceIP: d.LoadBalanceIP,
		IsSelf:        d.IsSelf,
		Workspace:     workspace,
		Status:        ToServerStatusRelationDTO(d.Status),
	}
}

func ToClusterDetailDTO(d *domain.Cluster) *ClusterDetailResponse {
	var workspace *WorkspaceRelationResponse
	if d.Workspace != nil {
		workspace = ToWorkspaceRelationDTO(d.Workspace)
	}

	var namespaces []NamespaceDetailResponse
	for _, ns := range d.Namespaces {
		namespaces = append(namespaces, *ToNamespaceDetailDTO(&ns))
	}

	return &ClusterDetailResponse{
		ID:            d.ID,
		CreatedAt:     d.CreatedAt,
		UpdatedAt:     d.UpdatedAt,
		Name:          d.Name,
		Region:        d.Region,
		PoolName:      d.PoolName,
		Size:          d.Size,
		NodeCount:     d.NodeCount,
		LoadBalanceIP: d.LoadBalanceIP,
		IsSelf:        d.IsSelf,
		Workspace:     workspace,
		Namespaces:    namespaces,
		Status:        ToServerStatusRelationDTO(d.Status),
	}
}

func ToClusterRelationDTO(d *domain.Cluster) *ClusterRelationResponse {
	if d == nil {
		return nil
	}
	return &ClusterRelationResponse{
		ID:            d.ID,
		Name:          d.Name,
		Region:        d.Region,
		PoolName:      d.PoolName,
		Size:          d.Size,
		NodeCount:     d.NodeCount,
		LoadBalanceIP: d.LoadBalanceIP,
		IsSelf:        d.IsSelf,
		Status:        ToServerStatusRelationDTO(d.Status),
	}
}
