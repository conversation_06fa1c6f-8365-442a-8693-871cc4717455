# Blacking Ops Admin API

A robust operations administration API built with Go using hexagonal architecture principles. This API provides comprehensive user management, job processing, infrastructure management, and administrative operations for operations teams.

## 🏗️ Architecture

This project follows the **Hexagonal Architecture** (Ports and Adapters) pattern, ensuring clean separation of concerns and testability:

```
├── cmd/                    # Application entry points
│   ├── main.go            # Main application entry
│   └── seed/              # Database seeding utilities
├── config/                 # Configuration management
├── internal/
│   ├── adapters/          # External adapters (HTTP, DB)
│   │   ├── http/          # HTTP server, handlers, middleware, routes
│   │   └── repository/    # Database repositories
│   ├── core/              # Business logic (domain-driven)
│   │   ├── domain/        # Domain entities
│   │   ├── dto/           # Data transfer objects
│   │   ├── ports/         # Interfaces/contracts
│   │   └── services/      # Business logic services
├── pkg/                   # Shared utilities and packages
│   ├── database/          # Database connections (PostgreSQL/SQLite)
│   └── utils/             # Error handling, logging utilities
└── terraform/             # Infrastructure as Code
    ├── cluster/           # Kubernetes cluster management
    ├── modules/           # Reusable Terraform modules
    └── resource/          # Cloud resource definitions
```

## 🚀 Features

### Core Features
- **User Authentication & Authorization**: JWT-based auth with role-based access control
- **User Management**: Registration, profile management, and admin operations
- **User Types & Roles**: Flexible user role and permission system
- **Job Management**: Create, track, update, and manage operational jobs
- **Job Actions & Status**: Configurable job workflows and status tracking
- **Job Logging**: Comprehensive logging and audit trail for job executions
- **Health Checks**: Multi-level health monitoring (health, readiness, liveness)

### Infrastructure Management
- **Cluster Management**: Kubernetes cluster operations and monitoring
- **Deployment Management**: Application deployment lifecycle
- **Environment Management**: Multi-environment configuration and isolation
- **Namespace Management**: Kubernetes namespace operations
- **Service Management**: Microservice management and orchestration
- **Ingress Management**: Load balancer and ingress configuration
- **Workspace Management**: Team workspace organization

### Technical Features
- **RESTful API**: Clean, well-documented REST endpoints with Swagger
- **Database Support**: Both PostgreSQL and SQLite support
- **Environment Configuration**: Flexible configuration via environment variables
- **Infrastructure as Code**: Terraform modules for cloud deployment
- **Middleware Support**: Authentication, logging, and custom middleware

## 🛠️ Technology Stack

- **Language**: Go 1.23+
- **Web Framework**: Fiber v2 (Fast HTTP framework)
- **Database**: PostgreSQL/SQLite with GORM ORM
- **Authentication**: JWT tokens with middleware
- **Logging**: Uber Zap structured logging
- **Configuration**: Environment variables with godotenv
- **Infrastructure**: Terraform with Kubernetes support
- **Cloud**: CloudFlare integration ready
- **Testing**: Built-in Go testing + Postman collection

## 📋 Prerequisites

- Go 1.23 or higher
- PostgreSQL (optional, SQLite included for development)
- Docker (optional, for containerized deployment)
- Terraform (for infrastructure deployment)
- Make (optional, for using Makefile commands)

## 🔧 Installation & Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd ops-admin-api
   ```

2. **Install dependencies**
   ```bash
   go mod download
   ```

3. **Set up environment variables**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration
   ```

4. **Run database migrations**
   ```bash
   make migrate-up
   # or
   go run cmd/seed/main.go
   ```

5. **Start the server**
   ```bash
   make run
   # or
   go run cmd/main.go
   ```

## 🌍 Environment Variables

```env
# Server Configuration
PORT=8080
ENV=development

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=password
DB_NAME=ops_admin_db
DB_SSLMODE=disable

# JWT Configuration
JWT_SECRET=your-secret-key-here
JWT_EXPIRY=24h

# SQLite Configuration (alternative)
SQLITE_PATH=./hexagonal_db.db
```

## 📚 API Documentation

### Base URL
```
http://localhost:8080
```

### Authentication
All protected endpoints require a Bearer token in the Authorization header:
```
Authorization: Bearer <jwt_token>
```

## 🔗 API Endpoints

### Health Monitoring

#### Health Check Endpoints
These endpoints are used for monitoring and health checks without authentication.

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| `GET` | `/health` | Basic health check | ❌ |
| `GET` | `/health/ready` | Readiness probe for K8s | ❌ |
| `GET` | `/health/live` | Liveness probe for containers | ❌ |

**Example Response:**
```json
{
  "status": "healthy",
  "service": "ops-api"
}
```

---

### Authentication

#### Auth Endpoints
Public endpoints for user authentication.

| Method | Endpoint | Description | Auth Required |
|--------|----------|-------------|---------------|
| `POST` | `/api/v1/auth/register` | Register new user | ❌ |
| `POST` | `/api/v1/auth/login` | User login | ❌ |

**Register User:**
```bash
POST /api/v1/auth/register
Content-Type: application/json

{
  "name": "John Doe",
  "email": "<EMAIL>",
  "password": "securepassword123"
}
```

**Response:**
```json
{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "user": {
    "id": 1,
    "name": "John Doe",
    "email": "<EMAIL>",
    "user_type": {
      "id": 2,
      "name": "member",
      "description": "Standard user access"
    },
    "created_at": "2025-06-27T10:30:00Z"
  }
}
```

**Login User:**
```bash
POST /api/v1/auth/login
Content-Type: application/json

{
  "email": "<EMAIL>",
  "password": "securepassword123"
}
```

---

### User Management

#### User Profile Endpoints
Manage user profiles and account settings.

| Method | Endpoint | Description | Auth Required | Admin Only |
|--------|----------|-------------|---------------|------------|
| `GET` | `/api/v1/users/profile` | Get current user profile | ✅ | ❌ |
| `PUT` | `/api/v1/users/profile` | Update user profile | ✅ | ❌ |
| `PUT` | `/api/v1/users/change-password` | Change password | ✅ | ❌ |

**Get User Profile:**
```bash
GET /api/v1/users/profile
Authorization: Bearer <jwt_token>
```

**Update Profile:**
```bash
PUT /api/v1/users/profile
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "name": "Updated Name",
  "email": "<EMAIL>"
}
```

**Change Password:**
```bash
PUT /api/v1/users/change-password
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "current_password": "oldpassword",
  "new_password": "newpassword123"
}
```

#### Admin User Management
Administrative endpoints for user management.

| Method | Endpoint | Description | Auth Required | Admin Only |
|--------|----------|-------------|---------------|------------|
| `GET` | `/api/v1/users/` | List all users | ✅ | ✅ |
| `POST` | `/api/v1/users/` | Create new user | ✅ | ✅ |
| `DELETE` | `/api/v1/users/:id` | Delete user | ✅ | ✅ |

---

### User Types & Roles

#### User Type Management
Manage user roles and permission types.

| Method | Endpoint | Description | Auth Required | Admin Only |
|--------|----------|-------------|---------------|------------|
| `GET` | `/api/v1/user-types/` | List all user types | ✅ | ❌ |
| `GET` | `/api/v1/user-types/:id` | Get specific user type | ✅ | ❌ |
| `POST` | `/api/v1/user-types/` | Create user type | ✅ | ✅ |
| `PUT` | `/api/v1/user-types/:id` | Update user type | ✅ | ✅ |
| `DELETE` | `/api/v1/user-types/:id` | Delete user type | ✅ | ✅ |

**Create User Type:**
```bash
POST /api/v1/user-types/
Authorization: Bearer <admin_token>
Content-Type: application/json

{
  "name": "moderator",
  "description": "Moderator with limited admin access",
  "is_active": true,
  "is_admin": false
}
```

---

### Job Management

#### Job Operations
Create and manage operational jobs.

| Method | Endpoint | Description | Auth Required | Owner/Admin |
|--------|----------|-------------|---------------|-------------|
| `GET` | `/api/v1/jobs/` | List jobs (with filters) | ✅ | ❌ |
| `POST` | `/api/v1/jobs/` | Create new job | ✅ | ❌ |
| `GET` | `/api/v1/jobs/my` | Get current user's jobs | ✅ | ❌ |
| `GET` | `/api/v1/jobs/:id` | Get job details | ✅ | ❌ |
| `PUT` | `/api/v1/jobs/:id` | Update job | ✅ | ✅ |

**List Jobs with Filters:**
```bash
GET /api/v1/jobs/?my_jobs=false&name=backup&job_status_id=1
Authorization: Bearer <jwt_token>
```

**Query Parameters:**
- `my_jobs` (boolean): Filter for current user's jobs only
- `name` (string): Filter by job name
- `job_status_id` (integer): Filter by job status
- `job_action_id` (integer): Filter by job action

**Create Job:**
```bash
POST /api/v1/jobs/
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "name": "Database Backup Job",
  "description": "Automated database backup process",
  "job_action_id": 1,
  "job_status_id": 1,
  "event_id": 123
}
```

**Update Job:**
```bash
PUT /api/v1/jobs/1
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "name": "Updated Job Name",
  "description": "Updated description",
  "job_status_id": 2,
  "job_action_id": 1,
  "event_id": 456
}
```

---

### Job Logs

#### Job Logging & Audit Trail
Track job execution and maintain audit logs.

| Method | Endpoint | Description | Auth Required | Admin Only |
|--------|----------|-------------|---------------|------------|
| `POST` | `/api/v1/job-logs/` | Create job log entry | ✅ | ❌ |
| `GET` | `/api/v1/job-logs/:id` | Get specific job log | ✅ | ❌ |
| `GET` | `/api/v1/jobs/:id/logs` | Get logs for specific job | ✅ | ❌ |

**Create Job Log:**
```bash
POST /api/v1/job-logs/
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "name": "Job Execution Step",
  "description": "Job started successfully",
  "job_id": 1
}
```

**Get Job Logs:**
```bash
GET /api/v1/jobs/1/logs
Authorization: Bearer <jwt_token>
```

---

### Infrastructure Management

#### Cluster Management
Create and manage Kubernetes clusters for your infrastructure.

| Method | Endpoint | Description | Auth Required | 
|--------|----------|-------------|---------------|
| `POST` | `/api/v1/clusters/` | Create new cluster | ✅ |
| `GET` | `/api/v1/clusters/` | List all clusters (with filters) | ✅ |
| `GET` | `/api/v1/clusters/:id` | Get cluster details | ✅ |
| `PUT` | `/api/v1/clusters/:id` | Update cluster | ✅ |
| `DELETE` | `/api/v1/clusters/:id` | Delete cluster | ✅ |

**List Clusters with Filters:**
```bash
GET /api/v1/clusters/?name=prod&region=us-east-1&workspace_id=1
Authorization: Bearer <jwt_token>
```

**Query Parameters:**
- `name` (string): Filter by cluster name (partial match)
- `region` (string): Filter by exact region
- `workspace_id` (integer): Filter by workspace ID

**Create Cluster:**
```bash
POST /api/v1/clusters/
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "name": "production-cluster",
  "region": "us-east-1",
  "pool_name": "default-pool",
  "size": "n1-standard-2",
  "node_count": 3,
  "workspace_id": 1
}
```

**Update Cluster:**
```bash
PUT /api/v1/clusters/1
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "name": "updated-cluster",
  "region": "us-west-2",
  "pool_name": "updated-pool",
  "size": "n1-standard-4",
  "node_count": 5,
  "workspace_id": 1
}
```

**Example Response (List):**
```json
{
  "status": "success",
  "message": "Clusters retrieved successfully",
  "data": [
    {
      "id": 1,
      "created_at": "2025-06-27T10:30:00Z",
      "updated_at": "2025-06-27T10:30:00Z",
      "name": "production-cluster",
      "region": "us-east-1",
      "pool_name": "default-pool",
      "size": "n1-standard-2",
      "node_count": 3,
      "workspace": {
        "id": 1,
        "name": "Production Workspace",
        "description": "Main production environment"
      }
    }
  ]
}
```

**Example Response (Detail):**
```json
{
  "status": "success",
  "message": "Cluster retrieved successfully",
  "data": {
    "id": 1,
    "created_at": "2025-06-27T10:30:00Z",
    "updated_at": "2025-06-27T10:30:00Z",
    "name": "production-cluster",
    "region": "us-east-1",
    "pool_name": "default-pool",
    "size": "n1-standard-2",
    "node_count": 3,
    "workspace": {
      "id": 1,
      "name": "Production Workspace",
      "description": "Main production environment"
    },
    "namespaces": [
      {
        "id": 1,
        "name": "default",
        "description": "Default namespace"
      },
      {
        "id": 2,
        "name": "kube-system",
        "description": "System namespace"
      }
    ]
  }
}
```

---

#### Deployment Management
Manage application deployments within your Kubernetes namespaces.

| Method | Endpoint | Description | Auth Required | 
|--------|----------|-------------|---------------|
| `POST` | `/api/v1/deployments/` | Create new deployment | ✅ |
| `GET` | `/api/v1/deployments/` | List all deployments (with filters) | ✅ |
| `GET` | `/api/v1/deployments/:id` | Get deployment details | ✅ |
| `PUT` | `/api/v1/deployments/:id` | Update deployment | ✅ |
| `DELETE` | `/api/v1/deployments/:id` | Delete deployment | ✅ |

**List Deployments with Filters:**
```bash
GET /api/v1/deployments/?name=web-app&namespace_id=1
Authorization: Bearer <jwt_token>
```

**Query Parameters:**
- `name` (string): Filter by deployment name (partial match)
- `namespace_id` (integer): Filter by namespace ID

**Create Deployment:**
```bash
POST /api/v1/deployments/
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "name": "web-application",
  "image": "nginx:1.21",
  "container_port": 80,
  "namespace_id": 1
}
```

**Update Deployment:**
```bash
PUT /api/v1/deployments/1
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "name": "updated-web-app",
  "image": "nginx:1.22",
  "container_port": 8080,
  "namespace_id": 1
}
```

**Example Response (List):**
```json
{
  "status": "success",
  "message": "Deployments retrieved successfully",
  "data": [
    {
      "id": 1,
      "created_at": "2025-06-27T10:30:00Z",
      "updated_at": "2025-06-27T10:30:00Z",
      "name": "web-application",
      "image": "nginx:1.21",
      "container_port": 80,
      "namespace": {
        "id": 1,
        "name": "production",
        "slug": "production"
      }
    }
  ]
}
```

**Example Response (Detail):**
```json
{
  "status": "success",
  "message": "Deployment retrieved successfully",
  "data": {
    "id": 1,
    "created_at": "2025-06-27T10:30:00Z",
    "updated_at": "2025-06-27T10:30:00Z",
    "name": "web-application",
    "image": "nginx:1.21",
    "container_port": 80,
    "namespace": {
      "id": 1,
      "name": "production",
      "slug": "production"
    }
  }
}
```

**Security Features:**
- Users can only create deployments in namespaces within their workspaces
- Workspace access is validated through cluster ownership
- All deployment operations require valid JWT authentication
- Input validation ensures valid container ports (1-65535) and required fields

---

#### Environment Management
Manage environment variables and configuration for your deployments.

| Method | Endpoint | Description | Auth Required | 
|--------|----------|-------------|---------------|
| `POST` | `/api/v1/environments/` | Create new environment variable | ✅ |
| `GET` | `/api/v1/environments/` | List all environments (with filters) | ✅ |
| `GET` | `/api/v1/environments/:id` | Get environment details | ✅ |
| `PUT` | `/api/v1/environments/:id` | Update environment variable | ✅ |
| `DELETE` | `/api/v1/environments/:id` | Delete environment variable | ✅ |

**List Environments with Filters:**
```bash
GET /api/v1/environments/?name=DATABASE&deployment_id=1
Authorization: Bearer <jwt_token>
```

**Query Parameters:**
- `name` (string): Filter by environment variable name (partial match)
- `deployment_id` (integer): Filter by deployment ID

**Create Environment:**
```bash
POST /api/v1/environments/
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "name": "DATABASE_URL",
  "value": "postgresql://localhost:5432/myapp",
  "deployment_id": 1
}
```

**Update Environment:**
```bash
PUT /api/v1/environments/1
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "name": "DATABASE_URL",
  "value": "postgresql://prod-db:5432/myapp_prod",
  "deployment_id": 1
}
```

**Example Response (List):**
```json
{
  "status": "success",
  "message": "Environments retrieved successfully",
  "data": [
    {
      "id": 1,
      "created_at": "2025-06-27T10:30:00Z",
      "updated_at": "2025-06-27T10:30:00Z",
      "name": "DATABASE_URL",
      "value": "postgresql://localhost:5432/myapp",
      "deployment": {
        "id": 1,
        "name": "web-application",
        "image": "nginx:1.21",
        "container_port": 80
      }
    },
    {
      "id": 2,
      "created_at": "2025-06-27T10:30:00Z",
      "updated_at": "2025-06-27T10:30:00Z",
      "name": "REDIS_URL",
      "value": "redis://localhost:6379",
      "deployment": {
        "id": 1,
        "name": "web-application",
        "image": "nginx:1.21",
        "container_port": 80
      }
    }
  ]
}
```

**Example Response (Detail):**
```json
{
  "status": "success",
  "message": "Environment retrieved successfully",
  "data": {
    "id": 1,
    "created_at": "2025-06-27T10:30:00Z",
    "updated_at": "2025-06-27T10:30:00Z",
    "name": "DATABASE_URL",
    "value": "postgresql://localhost:5432/myapp",
    "deployment": {
      "id": 1,
      "name": "web-application",
      "image": "nginx:1.21",
      "container_port": 80
    }
  }
}
```

---

#### Service Management
Manage Kubernetes services within your namespaces for exposing applications.

| Method | Endpoint | Description | Auth Required | 
|--------|----------|-------------|---------------|
| `POST` | `/api/v1/services/` | Create new service | ✅ |
| `GET` | `/api/v1/services/` | List all services (with filters) | ✅ |
| `GET` | `/api/v1/services/:id` | Get service details | ✅ |
| `PUT` | `/api/v1/services/:id` | Update service | ✅ |
| `DELETE` | `/api/v1/services/:id` | Delete service | ✅ |

**List Services with Filters:**
```bash
GET /api/v1/services/?name=web-service&namespace_id=1
Authorization: Bearer <jwt_token>
```

**Query Parameters:**
- `name` (string): Filter by service name (partial match)
- `namespace_id` (integer): Filter by namespace ID

**Create Service:**
```bash
POST /api/v1/services/
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "name": "web-service",
  "port": "80",
  "target_port": "8080",
  "namespace_id": 1
}
```

**Update Service:**
```bash
PUT /api/v1/services/1
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "name": "updated-web-service",
  "port": "443",
  "target_port": "8443",
  "namespace_id": 1
}
```

**Example Response (List):**
```json
{
  "status": "success",
  "message": "Services retrieved successfully",
  "data": [
    {
      "id": 1,
      "created_at": "2025-06-27T10:30:00Z",
      "updated_at": "2025-06-27T10:30:00Z",
      "name": "web-service",
      "port": "80",
      "target_port": "8080",
      "namespace": {
        "id": 1,
        "name": "production",
        "slug": "production"
      }
    }
  ]
}
```

**Example Response (Detail):**
```json
{
  "status": "success",
  "message": "Service retrieved successfully",
  "data": {
    "id": 1,
    "created_at": "2025-06-27T10:30:00Z",
    "updated_at": "2025-06-27T10:30:00Z",
    "name": "web-service",
    "port": "80",
    "target_port": "8080",
    "namespace": {
      "id": 1,
      "name": "production",
      "slug": "production"
    }
  }
}
```

---

#### Ingress Management
Manage Kubernetes ingress controllers within your namespaces for load balancing and routing.

| Method | Endpoint | Description | Auth Required | 
|--------|----------|-------------|---------------|
| `POST` | `/api/v1/ingresses/` | Create new ingress | ✅ |
| `GET` | `/api/v1/ingresses/` | List all ingresses (with filters) | ✅ |
| `GET` | `/api/v1/ingresses/:id` | Get ingress details | ✅ |
| `PUT` | `/api/v1/ingresses/:id` | Update ingress | ✅ |
| `DELETE` | `/api/v1/ingresses/:id` | Delete ingress | ✅ |

**List Ingresses with Filters:**
```bash
GET /api/v1/ingresses/?name=web-ingress&namespace_id=1
Authorization: Bearer <jwt_token>
```

**Query Parameters:**
- `name` (string): Filter by ingress name (partial match)
- `namespace_id` (integer): Filter by namespace ID

**Create Ingress:**
```bash
POST /api/v1/ingresses/
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "name": "web-ingress",
  "namespace_id": 1
}
```

**Update Ingress:**
```bash
PUT /api/v1/ingresses/1
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "name": "updated-web-ingress",
  "namespace_id": 1
}
```

**Example Response (List):**
```json
{
  "status": "success",
  "message": "Ingresses retrieved successfully",
  "data": [
    {
      "id": 1,
      "created_at": "2025-06-28T10:30:00Z",
      "updated_at": "2025-06-28T10:30:00Z",
      "name": "web-ingress",
      "namespace": {
        "id": 1,
        "name": "production",
        "slug": "production"
      }
    }
  ]
}
```

**Example Response (Detail):**
```json
{
  "status": "success",
  "message": "Ingress retrieved successfully",
  "data": {
    "id": 1,
    "created_at": "2025-06-28T10:30:00Z",
    "updated_at": "2025-06-28T10:30:00Z",
    "name": "web-ingress",
    "namespace": {
      "id": 1,
      "name": "production",
      "slug": "production"
    }
  }
}
```

---

#### Ingress Spec Management
Manage routing rules that connect ingress controllers to services for traffic direction.

| Method | Endpoint | Description | Auth Required | 
|--------|----------|-------------|---------------|
| `POST` | `/api/v1/ingress-specs/` | Create new ingress routing rule | ✅ |
| `GET` | `/api/v1/ingress-specs/` | List all ingress specs (with filters) | ✅ |
| `GET` | `/api/v1/ingress-specs/:id` | Get ingress spec details | ✅ |
| `PUT` | `/api/v1/ingress-specs/:id` | Update ingress routing rule | ✅ |
| `DELETE` | `/api/v1/ingress-specs/:id` | Delete ingress routing rule | ✅ |

**List Ingress Specs with Filters:**
```bash
GET /api/v1/ingress-specs/?host=example.com&service_id=1&ingress_id=1
Authorization: Bearer <jwt_token>
```

**Query Parameters:**
- `host` (string): Filter by hostname (partial match)
- `path` (string): Filter by path (partial match)
- `service_id` (integer): Filter by service ID
- `ingress_id` (integer): Filter by ingress ID

**Create Ingress Spec:**
```bash
POST /api/v1/ingress-specs/
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "host": "api.example.com",
  "path": "/v1/users",
  "service_id": 1,
  "port": 80,
  "ingress_id": 1
}
```

**Update Ingress Spec:**
```bash
PUT /api/v1/ingress-specs/1
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "host": "api-v2.example.com",
  "path": "/v2/users",
  "service_id": 2,
  "port": 8080,
  "ingress_id": 1
}
```

**Example Response (List):**
```json
{
  "status": "success",
  "message": "Ingress specs retrieved successfully",
  "data": [
    {
      "id": 1,
      "created_at": "2025-06-28T10:30:00Z",
      "updated_at": "2025-06-28T10:30:00Z",
      "host": "api.example.com",
      "path": "/v1/users",
      "port": 80,
      "service": {
        "id": 1,
        "name": "user-service",
        "port": "80",
        "target_port": "8080"
      },
      "ingress": {
        "id": 1,
        "name": "main-ingress"
      }
    }
  ]
}
```

**Example Response (Detail):**
```json
{
  "status": "success",
  "message": "Ingress spec retrieved successfully",
  "data": {
    "id": 1,
    "created_at": "2025-06-28T10:30:00Z",
    "updated_at": "2025-06-28T10:30:00Z",
    "host": "api.example.com",
    "path": "/v1/users",
    "port": 80,
    "service": {
      "id": 1,
      "name": "user-service",
      "port": "80",
      "target_port": "8080"
    },
    "ingress": {
      "id": 1,
      "name": "main-ingress"
    }
  }
}
```

---

## 📋 HTTP Status Codes

The API uses standard HTTP status codes:

| Code | Status | Description |
|------|--------|-------------|
| `200` | OK | Request successful |
| `201` | Created | Resource created successfully |
| `400` | Bad Request | Invalid request data |
| `401` | Unauthorized | Authentication required |
| `403` | Forbidden | Insufficient permissions |
| `404` | Not Found | Resource not found |
| `409` | Conflict | Resource already exists |
| `500` | Internal Server Error | Server error |

## 🔒 Authentication & Authorization

### JWT Token Authentication
- All protected endpoints require a valid JWT token
- Token should be included in the `Authorization` header as `Bearer <token>`
- Tokens are obtained through login or registration
- Tokens expire based on the `JWT_EXPIRY` configuration

### Permission Levels
1. **Public** - No authentication required (health checks, auth endpoints)
2. **Authenticated** - Valid JWT token required
3. **Owner** - User can only access/modify their own resources
4. **Admin** - Administrative privileges required

### Security Features
- Password hashing using bcrypt
- JWT token-based authentication
- Role-based access control (RBAC)
- Input validation and sanitization
- Secure error responses (no sensitive data exposure)

## 📝 Error Response Format

All error responses follow a consistent format:

```json
{
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid request data",
    "details": "Email is required"
  },
  "timestamp": "2025-06-27T10:30:00Z",
  "path": "/api/v1/auth/register"
}
```

## 🔄 Rate Limiting

Currently, the API does not implement rate limiting, but it's planned for future releases. Consider implementing:

- User-based rate limiting
- IP-based rate limiting
- Endpoint-specific limits
- Burst protection
# blacking-ops-api
# blacking-ops-api
